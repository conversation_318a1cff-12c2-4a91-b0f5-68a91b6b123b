# Profile Completion Flow Test Guide

## 🧪 **Testing the Complete Profile Completion Flow**

### **Test Scenarios:**

#### **Scenario 1: New User with Phone Login**
1. **Clear app data** (or use fresh install)
2. **Open app** → Should show Dashboard with login prompt
3. **Login with phone number** → Enter any phone number
4. **Enter OTP** → Enter any 4-digit code (e.g., 1234)
5. **Expected Result:** Should navigate to Profile Completion Screen
6. **Check pre-population:** Phone field should be pre-filled
7. **Fill required fields:** Name and Email
8. **Save profile** → Should navigate to Dashboard
9. **Restart app** → Should go directly to Dashboard (no profile completion)

#### **Scenario 2: New User with <PERSON><PERSON> Login (Google)**
1. **Clear app data**
2. **Open app** → Dashboard with login prompt
3. **Click Google login** → Simulates email login
4. **Expected Result:** Should navigate to Profile Completion Screen
5. **Check pre-population:** Email field should be pre-filled
6. **Fill required fields:** Name and Phone (optional)
7. **Save profile** → Should navigate to Dashboard
8. **Restart app** → Should go directly to Dashboard

#### **Scenario 3: Existing User with Complete Profile**
1. **Complete Scenario 1 or 2 first**
2. **Logout** (if logout feature exists)
3. **Login again with same credentials**
4. **Expected Result:** Should go directly to Dashboard (skip profile completion)

#### **Scenario 4: Existing User with Incomplete Profile**
1. **Login with new credentials**
2. **Start profile completion but don't save**
3. **Close app**
4. **Restart app and login again**
5. **Expected Result:** Should show profile completion again

### **Expected Behavior:**

#### **Smart Pre-population:**
- **Phone Login:** Phone field pre-filled, ask for email + name
- **Email Login:** Email field pre-filled, ask for phone + name

#### **Data Persistence:**
- **Profile completion status** saved in SharedPreferences
- **User credentials** tracked for existing user detection
- **Profile data** persists across app sessions

#### **Navigation Logic:**
```
Login Success → Check Profile Status →
  If New User: Profile Completion → Dashboard
  If Existing + Complete: Dashboard
  If Existing + Incomplete: Profile Completion → Dashboard
```

### **Verification Points:**

#### **✅ Check These Work:**
1. **Form validation** (required fields)
2. **Smart pre-population** based on login method
3. **Data persistence** across app restarts
4. **Conditional navigation** after login
5. **Success messages** and user feedback
6. **Profile completion status** tracking

#### **🔍 Debug Information:**
- Check SharedPreferences for keys:
  - `is_logged_in`
  - `user_email` / `user_phone`
  - `login_method`
  - `profile_complete`
  - `user_profiles` (list of existing users)
  - `user_name`

### **Test Data:**
- **Phone Numbers:** +1234567890, +9876543210
- **Email Addresses:** <EMAIL>, <EMAIL>
- **OTP:** Any 4-digit code (1234, 5678, etc.)
- **Names:** John Doe, Jane Smith, etc.

### **Expected Files Modified:**
1. `auth_notifier.dart` - Profile completion logic
2. `login_bottom_sheet.dart` - Conditional navigation
3. `profile_completion_screen.dart` - Smart pre-population
4. `splash_screen.dart` - Profile completion flow handling

### **Success Criteria:**
✅ New users see profile completion
✅ Existing users with complete profiles skip it
✅ Data pre-populates correctly based on login method
✅ Profile completion status persists
✅ Navigation works correctly in all scenarios
