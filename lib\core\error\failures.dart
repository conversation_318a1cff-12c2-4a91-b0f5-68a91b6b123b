import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message);
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure(String message) : super(message);
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure(String message) : super(message);
}

/// Validation-related failures
class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message);
}

/// General application failures
class GeneralFailure extends Failure {
  const GeneralFailure(String message) : super(message);
}
