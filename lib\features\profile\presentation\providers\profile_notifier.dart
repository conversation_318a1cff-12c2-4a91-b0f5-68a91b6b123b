/// Profile state management using Riverpod StateNotifier
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// User profile data class
class UserProfile {
  final String? name;
  final String? email;
  final String? profileImage;
  final String? membershipLevel;
  final int? totalBookings;
  final int? completedStays;
  final int? rewardPoints;
  final int? upcomingBookings;
  final int? reviewsCount;
  
  const UserProfile({
    this.name,
    this.email,
    this.profileImage,
    this.membershipLevel,
    this.totalBookings,
    this.completedStays,
    this.rewardPoints,
    this.upcomingBookings,
    this.reviewsCount,
  });

  UserProfile copyWith({
    String? name,
    String? email,
    String? profileImage,
    String? membershipLevel,
    int? totalBookings,
    int? completedStays,
    int? rewardPoints,
    int? upcomingBookings,
    int? reviewsCount,
  }) {
    return UserProfile(
      name: name ?? this.name,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      membershipLevel: membershipLevel ?? this.membershipLevel,
      totalBookings: totalBookings ?? this.totalBookings,
      completedStays: completedStays ?? this.completedStays,
      rewardPoints: rewardPoints ?? this.rewardPoints,
      upcomingBookings: upcomingBookings ?? this.upcomingBookings,
      reviewsCount: reviewsCount ?? this.reviewsCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
        other.name == name &&
        other.email == email &&
        other.profileImage == profileImage &&
        other.membershipLevel == membershipLevel &&
        other.totalBookings == totalBookings &&
        other.completedStays == completedStays &&
        other.rewardPoints == rewardPoints &&
        other.upcomingBookings == upcomingBookings &&
        other.reviewsCount == reviewsCount;
  }

  @override
  int get hashCode => Object.hash(
        name,
        email,
        profileImage,
        membershipLevel,
        totalBookings,
        completedStays,
        rewardPoints,
        upcomingBookings,
        reviewsCount,
      );
}

/// Profile state class
class ProfileState {
  final UserProfile userProfile;
  final bool isLoading;
  final String? error;

  const ProfileState({
    this.userProfile = const UserProfile(),
    this.isLoading = true,
    this.error,
  });

  ProfileState copyWith({
    UserProfile? userProfile,
    bool? isLoading,
    String? error,
  }) {
    return ProfileState(
      userProfile: userProfile ?? this.userProfile,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileState &&
        other.userProfile == userProfile &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode => Object.hash(userProfile, isLoading, error);
}

/// Profile StateNotifier
class ProfileNotifier extends StateNotifier<ProfileState> {
  ProfileNotifier() : super(const ProfileState()) {
    loadUserProfile();
  }

  /// Load user profile data
  Future<void> loadUserProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Simulate API call with a delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data - in real app, fetch from API or local storage
      final userProfile = UserProfile(
        name: 'User001',
        email: '<EMAIL>',
        profileImage: null, // Set to null to show placeholder or provide a real URL
        membershipLevel: 'Gold',
        totalBookings: 12,
        completedStays: 9,
        rewardPoints: 2500,
        upcomingBookings: 2,
        reviewsCount: 7,
      );
      
      state = state.copyWith(
        userProfile: userProfile,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load profile: ${e.toString()}',
      );
    }
  }
  
  /// Update user profile information
  Future<void> updateProfile({
    String? name,
    String? email,
    String? profileImage,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Simulate API call with a delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Update the profile
      final updatedProfile = state.userProfile.copyWith(
        name: name,
        email: email,
        profileImage: profileImage,
      );
      
      state = state.copyWith(
        userProfile: updatedProfile,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update profile: ${e.toString()}',
      );
    }
  }
  
  /// Add reward points for demonstration
  void addRewardPoints(int points) {
    final currentPoints = state.userProfile.rewardPoints ?? 0;
    final updatedProfile = state.userProfile.copyWith(
      rewardPoints: currentPoints + points,
    );
    
    state = state.copyWith(userProfile: updatedProfile);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    await loadUserProfile();
  }
}

/// Profile provider
final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>(
  (ref) => ProfileNotifier(),
);
