/// Localization state management using Riverpod StateNotifier
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Localization state class
class LocalizationState {
  final Locale locale;
  final bool isLoading;

  const LocalizationState({
    this.locale = const Locale('en'),
    this.isLoading = false,
  });

  /// Check if current language is RTL
  bool get isRTL => locale.languageCode == 'ar';

  /// Get text direction based on current locale
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;

  LocalizationState copyWith({
    Locale? locale,
    bool? isLoading,
  }) {
    return LocalizationState(
      locale: locale ?? this.locale,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocalizationState &&
        other.locale == locale &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode => Object.hash(locale, isLoading);
}

/// Localization StateNotifier
class LocalizationNotifier extends StateNotifier<LocalizationState> {
  static const String _languageKey = 'selected_language';

  LocalizationNotifier() : super(const LocalizationState()) {
    _loadSavedLanguage();
  }

  /// Load saved language from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      state = state.copyWith(isLoading: true);
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey);
      if (languageCode != null) {
        state = state.copyWith(
          locale: Locale(languageCode),
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      // Handle error silently, keep default locale
      debugPrint('Error loading saved language: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Save language to SharedPreferences
  Future<void> _saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      debugPrint('Error saving language: $e');
    }
  }

  /// Set new locale and save it
  Future<void> setLocale(Locale locale) async {
    if (state.locale == locale) return;
    
    state = state.copyWith(isLoading: true);
    await _saveLanguage(locale.languageCode);
    state = state.copyWith(
      locale: locale,
      isLoading: false,
    );
  }

  /// Get supported locales
  static List<Locale> get supportedLocales => const [
    Locale('en'), // English
    Locale('ar'), // Arabic
    Locale('es'), // Spanish
    Locale('fr'), // French
    Locale('hi'), // Hindi
  ];

  /// Check if locale is supported
  static bool isSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }
}

/// Localization provider
final localizationProvider = StateNotifierProvider<LocalizationNotifier, LocalizationState>(
  (ref) => LocalizationNotifier(),
);
