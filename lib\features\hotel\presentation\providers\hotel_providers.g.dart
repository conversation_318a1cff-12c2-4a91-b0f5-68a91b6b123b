// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hotel_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$hotelLocalDataSourceHash() =>
    r'd7882933c9d3e59d3b6dc9fd1d5f14615a84981f';

/// See also [hotelLocalDataSource].
@ProviderFor(hotelLocalDataSource)
final hotelLocalDataSourceProvider =
    AutoDisposeFutureProvider<HotelLocalDataSource>.internal(
  hotelLocalDataSource,
  name: r'hotelLocalDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hotelLocalDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HotelLocalDataSourceRef
    = AutoDisposeFutureProviderRef<HotelLocalDataSource>;
String _$hotelRemoteDataSourceHash() =>
    r'99d070a738bbbf44e391f82db61ca156ba69aee8';

/// See also [hotelRemoteDataSource].
@ProviderFor(hotelRemoteDataSource)
final hotelRemoteDataSourceProvider =
    AutoDisposeProvider<HotelRemoteDataSource>.internal(
  hotelRemoteDataSource,
  name: r'hotelRemoteDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hotelRemoteDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HotelRemoteDataSourceRef
    = AutoDisposeProviderRef<HotelRemoteDataSource>;
String _$hotelRepositoryHash() => r'2b590e5305551701f56452c088c651cccb4d4edb';

/// See also [hotelRepository].
@ProviderFor(hotelRepository)
final hotelRepositoryProvider =
    AutoDisposeFutureProvider<HotelRepository>.internal(
  hotelRepository,
  name: r'hotelRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hotelRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HotelRepositoryRef = AutoDisposeFutureProviderRef<HotelRepository>;
String _$getHotelsHash() => r'57fc5d39b1e8c3ee71e3be3c49178ed0b722f58c';

/// See also [getHotels].
@ProviderFor(getHotels)
final getHotelsProvider = AutoDisposeFutureProvider<GetHotels>.internal(
  getHotels,
  name: r'getHotelsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$getHotelsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetHotelsRef = AutoDisposeFutureProviderRef<GetHotels>;
String _$getFeaturedHotelsHash() => r'9db09be737aa9880fc346e9caa51e0105014982c';

/// See also [getFeaturedHotels].
@ProviderFor(getFeaturedHotels)
final getFeaturedHotelsProvider =
    AutoDisposeFutureProvider<GetFeaturedHotels>.internal(
  getFeaturedHotels,
  name: r'getFeaturedHotelsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getFeaturedHotelsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetFeaturedHotelsRef = AutoDisposeFutureProviderRef<GetFeaturedHotels>;
String _$getHotelDetailsHash() => r'8ba0dce034cf847dfe54bf4dc6c52fa7a2b458dd';

/// See also [getHotelDetails].
@ProviderFor(getHotelDetails)
final getHotelDetailsProvider =
    AutoDisposeFutureProvider<GetHotelDetails>.internal(
  getHotelDetails,
  name: r'getHotelDetailsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getHotelDetailsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetHotelDetailsRef = AutoDisposeFutureProviderRef<GetHotelDetails>;
String _$searchHotelsHash() => r'6e5f3ddd5aa7be6205a8dcd71da29e5f90418568';

/// See also [searchHotels].
@ProviderFor(searchHotels)
final searchHotelsProvider = AutoDisposeFutureProvider<SearchHotels>.internal(
  searchHotels,
  name: r'searchHotelsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchHotelsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchHotelsRef = AutoDisposeFutureProviderRef<SearchHotels>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
