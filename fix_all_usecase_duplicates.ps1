# Fix all UseCase files with duplicate content
Write-Host "Fixing UseCase files with duplicate content..." -ForegroundColor Cyan

$usecaseFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart" | Where-Object {
    $_.FullName -match "usecases" -and $_.FullName -notmatch "base"
}

$fixedFiles = 0

foreach ($file in $usecaseFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check if file has duplicate content (imports after class declarations)
    if ($content -match "class.*\{.*import\s+" -or $content -match "\}\s*import\s+") {
        Write-Host "Fixing duplicates in: $($file.Name)" -ForegroundColor Yellow
        
        $lines = Get-Content $file.FullName
        $cleanLines = @()
        $foundFirstClass = $false
        $hasUseCaseImport = $false
        
        # First pass: collect lines until we hit duplicate imports
        for ($i = 0; $i -lt $lines.Length; $i++) {
            $line = $lines[$i]
            
            # Check if we already have UseCase import
            if ($line -match "shared/domain/usecases/base/usecase.dart") {
                $hasUseCaseImport = $true
            }
            
            # If we hit a class declaration, mark it
            if ($line -match "^(class|abstract class)" -and -not $foundFirstClass) {
                $foundFirstClass = $true
            }
            
            # If we found first class and now hit an import, stop here (duplicate section)
            if ($foundFirstClass -and $line -match "^import\s+") {
                break
            }
            
            $cleanLines += $line
        }
        
        # Add UseCase import if missing
        if (-not $hasUseCaseImport) {
            $finalLines = @()
            $importAdded = $false
            
            for ($i = 0; $i -lt $cleanLines.Length; $i++) {
                $line = $cleanLines[$i]
                $finalLines += $line
                
                # Add UseCase import after the last import
                if ($line -match "^import\s+" -and -not $importAdded) {
                    $nextLineIsImport = $false
                    if ($i + 1 -lt $cleanLines.Length) {
                        $nextLineIsImport = $cleanLines[$i + 1] -match "^import\s+"
                    }
                    
                    if (-not $nextLineIsImport) {
                        $finalLines += "import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';"
                        $importAdded = $true
                    }
                }
            }
            $cleanLines = $finalLines
        }
        
        # Write cleaned content
        $cleanLines | Set-Content $file.FullName -Encoding UTF8
        $fixedFiles++
        Write-Host "Fixed: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Fixed $fixedFiles UseCase files" -ForegroundColor Yellow
