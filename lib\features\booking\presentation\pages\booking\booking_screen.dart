import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/booking/presentation/providers/booking_notifier.dart';
import 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart';
import 'package:kind_ali/features/authentication/presentation/pages/authentication/login_bottom_sheet.dart';
import 'package:kind_ali/features/booking/presentation/pages/payment/payment_screen.dart';
import 'package:kind_ali/features/booking/presentation/pages/booking/widgets/discount_coupons_page_widget.dart';
import 'package:kind_ali/features/booking/presentation/pages/booking/widgets/cancellation_policy_widget.dart';
import 'package:kind_ali/features/booking/presentation/pages/booking/widgets/special_request_widget.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';

class BookingScreen extends ConsumerStatefulWidget {
  final InventoryInfoList? hotel;
  final String? roomType;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int adultCount;
  final int childCount;
  final List<SelectedRoomOption>? selectedRoomOptions;
  final double? totalPrice;

  const BookingScreen({
    super.key,
    this.hotel,
    this.roomType,
    this.checkInDate,
    this.checkOutDate,
    this.adultCount = 2,
    this.childCount = 0,
    this.selectedRoomOptions,
    this.totalPrice,
  });

  @override
  ConsumerState<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends ConsumerState<BookingScreen> {

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final authState = ref.read(authProvider);

      if (!authState.isLoggedIn && mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          isDismissible: false,
          enableDrag: false,
          builder: (context) => PopScope(
            canPop: false,
            child: const LoginBottomSheet(),
          ),
        ).then((_) {
          final updatedAuthState = ref.read(authProvider);
          if (!updatedAuthState.isLoggedIn && mounted) {
            Navigator.of(context).pop();
          } else {
            _initializeBookingData();
          }
        });
      } else {
        _initializeBookingData();
      }
    });

    ref.read(bookingProvider.notifier).loadHotelOffers();
  }
  
  void _initializeBookingData() {
    double roomCharges = widget.totalPrice ?? 0.0;
    String roomType = widget.roomType ?? 'Selected Rooms';

    if (widget.selectedRoomOptions != null && widget.selectedRoomOptions!.isNotEmpty) {
      roomType = widget.selectedRoomOptions!.first.roomType;
    }

    ref.read(bookingProvider.notifier).initBooking(
      hotel: widget.hotel,
      roomType: roomType,
      checkInDate: widget.checkInDate,
      checkOutDate: widget.checkOutDate,
      adultCount: widget.adultCount,
      childCount: widget.childCount,
    );

    if (roomCharges > 0) {
      ref.read(bookingProvider.notifier).setRoomCharges(roomCharges);
    }
  }

  @override
  Widget build(BuildContext context) {
    final bookingState = ref.watch(bookingProvider);
    final bookingNotifier = ref.read(bookingProvider.notifier);

    // Ensure we're using the actual selected dates from widget parameters first
    final checkInDate = widget.checkInDate ?? bookingState.checkInDate;
    final checkOutDate = widget.checkOutDate ?? bookingState.checkOutDate;
    final hotel = widget.hotel ?? bookingState.hotel;
        
        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            title: const Text('Book Your Stay'),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
            ),
          
          ),  actions: [
              IconButton(
                icon: const Icon(Icons.discount),
                onPressed: () {
                  // TODO: Fix DiscountCouponsPageWidget to use Riverpod
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Coupon feature temporarily disabled during migration')),
                  );
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Hotel summary card
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: hotel?.imageInfoList?.isNotEmpty == true
                                ? Image.network(
                                    hotel!.imageInfoList!.first.url ?? '',
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 80,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade200,
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: const Icon(Icons.hotel, color: Colors.grey),
                                      );
                                    },
                                  )
                                : Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(Icons.hotel, color: Colors.grey),
                                  ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  hotel?.name ?? 'Selected Hotel',
                                  style: AppTextStyles.headline3,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(Icons.location_on, size: 16, color: AppColors.textLight),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        hotel?.locality != null && hotel?.city != null
                                            ? '${hotel!.locality}, ${hotel.city}'
                                            : hotel?.city != null
                                                ? hotel!.city!
                                                : hotel?.locality != null
                                                    ? hotel!.locality!
                                                    : 'Location not specified',
                                        style: TextStyle(color: AppColors.textLight, fontSize: 12),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(Icons.star, size: 16, color: AppColors.accent),
                                    const SizedBox(width: 4),
                                    Text(
                                      hotel?.userRating != null
                                          ? '${hotel!.userRating!} (${hotel!.userRatingCount ?? 0} reviews)'
                                          : 'No rating available',
                                      style: TextStyle(color: AppColors.textLight, fontSize: 12),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 24),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Check-in',
                                      style: TextStyle(
                                        color: AppColors.textLight,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  checkInDate != null
                                      ? DateFormat('MMM dd, yyyy').format(checkInDate)
                                      : 'Not selected',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Check-out',
                                      style: TextStyle(
                                        color: AppColors.textLight,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  checkOutDate != null
                                      ? DateFormat('MMM dd, yyyy').format(checkOutDate)
                                      : 'Not selected',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.person, size: 16, color: AppColors.primary),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Guests',
                                      style: TextStyle(
                                        color: AppColors.textLight,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${bookingState.adultCount} ${bookingState.adultCount == 1 ? 'Adult' : 'Adults'}${bookingState.childCount > 0 ? ', ${bookingState.childCount} ${bookingState.childCount == 1 ? 'Child' : 'Children'}' : ''}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 24),
                      if (widget.selectedRoomOptions != null && widget.selectedRoomOptions!.isNotEmpty) ...[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${'itinerary.rooms'.tr} (${widget.selectedRoomOptions!.fold(0, (sum, option) => sum + option.quantity)})',
                              style: AppTextStyles.subtitle1.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...widget.selectedRoomOptions!.map((selectedOption) =>
                              Container(
                                margin: const EdgeInsets.only(bottom: 8),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppColors.neutralLight,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.grey.shade200),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            selectedOption.room.name ?? 'Room',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            selectedOption.roomOption.name ?? 'Option',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          if (selectedOption.roomOption.mealBenefits != null &&
                                              selectedOption.roomOption.mealBenefits!.isNotEmpty)
                                            Text(
                                              selectedOption.roomOption.mealBenefits!.join(', '),
                                              style: TextStyle(
                                                fontSize: 11,
                                                color: Colors.green.shade700,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          'Qty: ${selectedOption.quantity}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          '₹${((selectedOption.roomOption.fareDetail?.displayedBaseFare ?? 0.0) * selectedOption.quantity).toInt()}',
                                          style: AppTextStyles.subtitle1.copyWith(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  bookingState.roomType,
                                  style: AppTextStyles.subtitle1,
                                ),
                                Text(
                                  '\$${(bookingState.roomCharges / bookingState.numberOfNights).toStringAsFixed(0)}/night',
                                  style: AppTextStyles.subtitle1.copyWith(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Breakfast included',
                              style: TextStyle(color: AppColors.textLight),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                // Guest details form
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Form(
                    key: bookingState.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter Your Details',
                          style: AppTextStyles.headline3.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                bookingNotifier.setSelectedTitle('Mr.');
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: bookingState.selectedTitle == 'Mr.' ? AppColors.primary : Colors.grey.shade400,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: bookingState.selectedTitle == 'Mr.'
                                        ? Container(
                                            margin: const EdgeInsets.all(3),
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: AppColors.primary,
                                            ),
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Mr.',
                                    style: TextStyle(
                                      color: bookingState.selectedTitle == 'Mr.' ? AppColors.primary : Colors.grey.shade700,
                                      fontWeight: bookingState.selectedTitle == 'Mr.' ? FontWeight.w500 : FontWeight.normal,
                                      fontSize: 15,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            GestureDetector(
                              onTap: () {
                                bookingNotifier.setSelectedTitle('Mrs.');
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: bookingState.selectedTitle == 'Mrs.' ? AppColors.primary : Colors.grey.shade400,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: bookingState.selectedTitle == 'Mrs.'
                                        ? Container(
                                            margin: const EdgeInsets.all(3),
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: AppColors.primary,
                                            ),
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Mrs.',
                                    style: TextStyle(
                                      color: bookingState.selectedTitle == 'Mrs.' ? AppColors.primary : Colors.grey.shade700,
                                      fontWeight: bookingState.selectedTitle == 'Mrs.' ? FontWeight.w500 : FontWeight.normal,
                                      fontSize: 15,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            GestureDetector(
                              onTap: () {
                                bookingNotifier.setSelectedTitle('Miss.');
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: bookingState.selectedTitle == 'Miss.' ? AppColors.primary : Colors.grey.shade400,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: bookingState.selectedTitle == 'Miss.'
                                        ? Container(
                                            margin: const EdgeInsets.all(3),
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: AppColors.primary,
                                            ),
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Miss.',
                                    style: TextStyle(
                                      color: bookingState.selectedTitle == 'Miss.' ? AppColors.primary : Colors.grey.shade700,
                                      fontWeight: bookingState.selectedTitle == 'Miss.' ? FontWeight.w500 : FontWeight.normal,
                                      fontSize: 15,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: bookingState.firstNameController,
                                style: const TextStyle(fontSize: 16),
                                decoration: InputDecoration(
                                  labelText: 'booking.firstName'.tr,
                                  labelStyle: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                  filled: false,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.red.shade300),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your first name';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: TextFormField(
                                controller: bookingState.lastNameController,
                                style: const TextStyle(fontSize: 16),
                                decoration: InputDecoration(
                                  labelText: 'booking.lastName'.tr,
                                  labelStyle: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                  filled: false,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.red.shade300),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your last name';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        TextFormField(
                          controller: bookingState.emailController,
                          keyboardType: TextInputType.emailAddress,
                          style: const TextStyle(fontSize: 16),
                          decoration: InputDecoration(
                            labelText: 'auth.email'.tr,
                            labelStyle: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                            filled: false,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.red.shade300),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email address';
                            } else if (!value.contains('@') || !value.contains('.')) {
                              return 'Please enter a valid email address';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 56,
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  value: bookingState.selectedCountryCode,
                                  icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade700),
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  items: ['+91', '+1', '+44', '+61', '+86'].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    if (newValue != null) {
                                      bookingNotifier.setSelectedCountryCode(newValue);
                                    }
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextFormField(
                                controller: bookingState.phoneController,
                                keyboardType: TextInputType.phone,
                                style: const TextStyle(fontSize: 16),
                                decoration: InputDecoration(
                                  labelText: 'booking.phoneNumber'.tr,
                                  labelStyle: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                  filled: false,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.red.shade300),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your phone number';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Container(
                          height: 56,
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              isExpanded: true,
                              hint: Text(
                                'Nationality',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 16,
                                ),
                              ),
                              value: bookingState.selectedNationality,
                              icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade700),
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              items: bookingState.nationalities.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null) {
                                  bookingNotifier.setSelectedNationality(newValue);
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Others section
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Booking for Others',
                            style: AppTextStyles.headline3.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Switch(
                            value: bookingState.isBookingForOthers,
                            onChanged: (value) {
                              bookingNotifier.setIsBookingForOthers(value);
                            },
                            activeColor: AppColors.primary,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'You can book rooms for other guests while keeping your contact details for confirmation',
                        style: TextStyle(
                          color: AppColors.textLight,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (bookingState.isBookingForOthers) ...[
                        ...List.generate(
                          widget.selectedRoomOptions?.length ?? 1,
                          (index) => Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.neutralLight,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.selectedRoomOptions != null && 
                                  widget.selectedRoomOptions!.length > index
                                      ? widget.selectedRoomOptions![index].room.name ?? 'Room ${index + 1}'
                                      : 'Room ${index + 1}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Row(
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        bookingNotifier.setGuestTitle(index, 'Mr.');
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: bookingNotifier.getGuestTitle(index) == 'Mr.' ? AppColors.primary : Colors.grey.shade400,
                                                width: 1.5,
                                              ),
                                            ),
                                            child: bookingNotifier.getGuestTitle(index) == 'Mr.'
                                                ? Container(
                                                    margin: const EdgeInsets.all(3),
                                                    decoration: const BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: AppColors.primary,
                                                    ),
                                                  )
                                                : null,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Mr.',
                                            style: TextStyle(
                                              color: bookingNotifier.getGuestTitle(index) == 'Mr.' ? AppColors.primary : Colors.grey.shade700,
                                              fontWeight: bookingNotifier.getGuestTitle(index) == 'Mr.' ? FontWeight.w500 : FontWeight.normal,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    GestureDetector(
                                      onTap: () {
                                        bookingNotifier.setGuestTitle(index, 'Mrs.');
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: bookingNotifier.getGuestTitle(index) == 'Mrs.' ? AppColors.primary : Colors.grey.shade400,
                                                width: 1.5,
                                              ),
                                            ),
                                            child: bookingNotifier.getGuestTitle(index) == 'Mrs.'
                                                ? Container(
                                                    margin: const EdgeInsets.all(3),
                                                    decoration: const BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: AppColors.primary,
                                                    ),
                                                  )
                                                : null,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Mrs.',
                                            style: TextStyle(
                                              color: bookingNotifier.getGuestTitle(index) == 'Mrs.' ? AppColors.primary : Colors.grey.shade700,
                                              fontWeight: bookingNotifier.getGuestTitle(index) == 'Mrs.' ? FontWeight.w500 : FontWeight.normal,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    GestureDetector(
                                      onTap: () {
                                        bookingNotifier.setGuestTitle(index, 'Miss.');
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: bookingNotifier.getGuestTitle(index) == 'Miss.' ? AppColors.primary : Colors.grey.shade400,
                                                width: 1.5,
                                              ),
                                            ),
                                            child: bookingNotifier.getGuestTitle(index) == 'Miss.'
                                                ? Container(
                                                    margin: const EdgeInsets.all(3),
                                                    decoration: const BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: AppColors.primary,
                                                    ),
                                                  )
                                                : null,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Miss.',
                                            style: TextStyle(
                                              color: bookingNotifier.getGuestTitle(index) == 'Miss.' ? AppColors.primary : Colors.grey.shade700,
                                              fontWeight: bookingNotifier.getGuestTitle(index) == 'Miss.' ? FontWeight.w500 : FontWeight.normal,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        controller: bookingNotifier.getGuestFirstNameController(index),
                                        style: const TextStyle(fontSize: 16),
                                        decoration: InputDecoration(
                                          labelText: 'First Name',
                                          labelStyle: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 16,
                                          ),
                                          filled: false,
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                                          ),
                                          errorBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.red.shade300),
                                          ),
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                        ),
                                        validator: (value) {
                                          if (bookingState.isBookingForOthers &&
                                              (value == null || value.isEmpty)) {
                                            return 'Required';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: TextFormField(
                                        controller: bookingNotifier.getGuestLastNameController(index),
                                        style: const TextStyle(fontSize: 16),
                                        decoration: InputDecoration(
                                          labelText: 'Last Name',
                                          labelStyle: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 16,
                                          ),
                                          filled: false,
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                                          ),
                                          errorBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: BorderSide(color: Colors.red.shade300),
                                          ),
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                        ),
                                        validator: (value) {
                                          if (bookingState.isBookingForOthers &&
                                              (value == null || value.isEmpty)) {
                                            return 'Required';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                CancellationPolicyWidget(
                  checkInDate: bookingState.checkInDate != null
                      ? DateFormat('MMM dd, yyyy').format(bookingState.checkInDate!)
                      : 'May 15, 2023',
                  checkInTime: '2:00 PM',
                ),
                SpecialRequestWidget(
                  onRequestSubmitted: (request) {
                    bookingNotifier.setSpecialRequest(request);
                  },
                ),
                totalamount(bookingState, bookingNotifier, context),
              ],
            ),
          ),
        );
  }

  Container totalamount(BookingState bookingState, BookingNotifier bookingNotifier, BuildContext context) {
    // Calculate proper room charges and taxes from selected room options
    double roomCharges = 0.0;
    double taxesAndFees = 0.0;
    
    if (widget.selectedRoomOptions != null) {
      for (var selectedOption in widget.selectedRoomOptions!) {
        // Use the price from the SelectedRoomOption (which should include base fare)
        final baseFare = selectedOption.price;
        // For now, assume 18% tax (this should be configurable)
        final tax = baseFare * 0.18;

        // Multiply by quantity and add to totals
        roomCharges += baseFare * selectedOption.quantity;
        taxesAndFees += tax * selectedOption.quantity;
      }
    } else {
      // Fallback to booking state values if no selected room options
      roomCharges = bookingState.roomCharges;
      taxesAndFees = bookingState.taxesAndFees;
    }

    final totalAmount = roomCharges + taxesAndFees - bookingState.couponDiscount;
    
    return Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(13),
                            blurRadius: 10,
                            offset: const Offset(0, 2),)
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Room Charges (${bookingState.numberOfNights} nights)',
                                style: const TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                '₹${roomCharges.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 14,
                                  color: AppColors.text,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Taxes & Fees',
                                style: const TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                '₹${taxesAndFees.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 14,
                                  color: AppColors.text,
                                ),
                              ),
                            ],
                          ),
                          if (bookingState.couponDiscount > 0) ...[
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Coupon Discount',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.normal,
                                    fontSize: 14,
                                  ),
                                ),
                                Text(
                                  '-₹${bookingState.couponDiscount.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontWeight: FontWeight.normal,
                                    fontSize: 14,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          const SizedBox(height: 8),
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => DiscountCouponsPageWidget(
                                    totalAmount: roomCharges + taxesAndFees,
                                    onCouponApplied: (code, discount) {
                                      bookingNotifier.applyCoupon(code, discount);
                                    },
                                  ),
                                ),
                              );
                            },
                            child: Row(
                              children: [
                                Icon(
                                  Icons.local_offer_outlined,
                                  size: 18,
                                  color: AppColors.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  bookingState.appliedCouponCode != null
                                      ? 'Coupon Applied: ${bookingState.appliedCouponCode}'
                                      : 'Apply Coupon',
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const Spacer(),
                                if (bookingState.appliedCouponCode != null)
                                  GestureDetector(
                                    onTap: () {
                                      // Remove the applied coupon
                                      bookingNotifier.removeCoupon();
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: const Text('Coupon removed'),
                                          backgroundColor: AppColors.primary,
                                          behavior: SnackBarBehavior.floating,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                        ),
                                      );
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        size: 16,
                                        color: AppColors.textLight,
                                      ),
                                    ),
                                  ),
                                if (bookingState.appliedCouponCode == null)
                                  Icon(
                                    Icons.chevron_right,
                                    size: 18,
                                    color: AppColors.primary,
                                  ),
                              ],
                            ),
                          ),
                          const Divider(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'itinerary.totalAmount'.tr,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                '₹${totalAmount.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    CustombuttonWidget(
                      text: 'Continue to Payment',
                      backgroundColor: AppColors.primary,
                      textColor: Colors.white,
                      borderRadius: 8,
                      height: 56,
                      isFullWidth: true,
                      onPressed: () {
                        if (bookingNotifier.validateForm()) {
                          if (bookingState.isBookingForOthers) {
                            bool allGuestsValid = true;
                            for (int i = 0; i < (widget.selectedRoomOptions?.length ?? 1); i++) {
                              if (bookingNotifier.getGuestFirstNameController(i).text.isEmpty ||
                                  bookingNotifier.getGuestLastNameController(i).text.isEmpty) {
                                allGuestsValid = false;
                                break;
                              }
                            }
                            
                            if (!allGuestsValid) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please enter details for all guests'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }
                          }

                          // Use the actual selected hotel and dates from widget parameters first
                          final hotel = widget.hotel ?? bookingState.hotel;
                          final checkInDate = widget.checkInDate ?? bookingState.checkInDate;
                          final checkOutDate = widget.checkOutDate ?? bookingState.checkOutDate;
                          
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PaymentScreen(
                                totalAmount: totalAmount,
                                hotelName: hotel?.name ?? 'Luxury Resort & Spa',
                                roomType: widget.roomType ?? bookingState.roomType,
                                checkInDate: checkInDate != null
                                    ? DateFormat('MMM dd, yyyy').format(checkInDate)
                                    : 'Not selected',
                                checkOutDate: checkOutDate != null
                                    ? DateFormat('MMM dd, yyyy').format(checkOutDate)
                                    : 'Not selected',
                                selectedRoomOptions: widget.selectedRoomOptions,
                                guestName: bookingState.isBookingForOthers
                                    ? 'Multiple Guests'
                                    : '${bookingState.firstNameController.text} ${bookingState.lastNameController.text}',
                                guestEmail: bookingState.emailController.text,
                                guestPhone: '${bookingState.selectedCountryCode} ${bookingState.phoneController.text}',
                                additionalGuests: bookingState.isBookingForOthers
                                    ? _getGuestDetailsList(widget.selectedRoomOptions?.length ?? 1)
                                    : null,
                              ),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              );
  }

  /// Get guest details list for additional guests
  List<Map<String, String>> _getGuestDetailsList(int numberOfGuests) {
    final bookingNotifier = ref.read(bookingProvider.notifier);

    List<Map<String, String>> guestDetails = [];

    for (int i = 0; i < numberOfGuests; i++) {
      guestDetails.add({
        'title': bookingNotifier.getGuestTitle(i),
        'firstName': bookingNotifier.getGuestFirstNameController(i).text,
        'lastName': bookingNotifier.getGuestLastNameController(i).text,
      });
    }

    return guestDetails;
  }
}
