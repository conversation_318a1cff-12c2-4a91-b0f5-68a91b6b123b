﻿/// Search cities state management using Riverpod StateNotifier
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/services/location_service.dart';
import 'package:kind_ali/features/search/data/models/search_cities.dart';

/// Search cities state class
class SearchCitiesState {
  final SearchCitiesData? searchCitiesData;
  final List<SearchCity> filteredCities;
  final List<PopularPlace> filteredPopularPlaces;
  final String searchQuery;
  final bool isLoading;
  final String? currentLocation;
  final String? error;
  final List<String> recentSearches;

  const SearchCitiesState({
    this.searchCitiesData,
    this.filteredCities = const [],
    this.filteredPopularPlaces = const [],
    this.searchQuery = '',
    this.isLoading = false,
    this.currentLocation,
    this.error,
    this.recentSearches = const [],
  });

  /// Get popular cities (first 10 cities sorted by property count)
  List<SearchCity> get popularCities {
    if (searchCitiesData == null) return [];

    List<SearchCity> cities = List.from(searchCitiesData!.cities);
    cities.sort((a, b) => b.propertyCount.compareTo(a.propertyCount));
    return cities.take(10).toList();
  }

  SearchCitiesState copyWith({
    SearchCitiesData? searchCitiesData,
    List<SearchCity>? filteredCities,
    List<PopularPlace>? filteredPopularPlaces,
    String? searchQuery,
    bool? isLoading,
    String? currentLocation,
    String? error,
    List<String>? recentSearches,
  }) {
    return SearchCitiesState(
      searchCitiesData: searchCitiesData ?? this.searchCitiesData,
      filteredCities: filteredCities ?? this.filteredCities,
      filteredPopularPlaces: filteredPopularPlaces ?? this.filteredPopularPlaces,
      searchQuery: searchQuery ?? this.searchQuery,
      isLoading: isLoading ?? this.isLoading,
      currentLocation: currentLocation ?? this.currentLocation,
      error: error,
      recentSearches: recentSearches ?? this.recentSearches,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchCitiesState &&
        other.searchCitiesData == searchCitiesData &&
        other.filteredCities == filteredCities &&
        other.filteredPopularPlaces == filteredPopularPlaces &&
        other.searchQuery == searchQuery &&
        other.isLoading == isLoading &&
        other.currentLocation == currentLocation &&
        other.error == error &&
        other.recentSearches == recentSearches;
  }

  @override
  int get hashCode => Object.hash(
        searchCitiesData,
        filteredCities,
        filteredPopularPlaces,
        searchQuery,
        isLoading,
        currentLocation,
        error,
        recentSearches,
      );
}

/// Search cities StateNotifier
class SearchCitiesNotifier extends StateNotifier<SearchCitiesState> {
  // SharedPreferences key for recent searches
  static const String _recentSearchesKey = 'recent_searches';

  SearchCitiesNotifier() : super(const SearchCitiesState()) {
    loadSearchCitiesData();
    loadRecentSearches();
  }

  /// Load search cities data from JSON
  Future<void> loadSearchCitiesData() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final String jsonString = await rootBundle.loadString('assets/json/serachcities.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      
      final searchCitiesData = SearchCitiesData.fromJson(jsonData);
      
      state = state.copyWith(
        searchCitiesData: searchCitiesData,
        filteredCities: searchCitiesData.cities,
        filteredPopularPlaces: searchCitiesData.popularPlaces,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load cities data: $e',
        isLoading: false,
      );
    }
  }

  /// Search functionality
  void searchCities(String query) {
    if (state.searchCitiesData == null) return;

    if (query.isEmpty) {
      state = state.copyWith(
        searchQuery: query,
        filteredCities: state.searchCitiesData!.cities,
        filteredPopularPlaces: state.searchCitiesData!.popularPlaces,
      );
    } else {
      final lowercaseQuery = query.toLowerCase();
      
      final filteredCities = state.searchCitiesData!.cities.where((city) {
        return city.name.toLowerCase().contains(lowercaseQuery) ||
               city.country.toLowerCase().contains(lowercaseQuery);
      }).toList();

      final filteredPopularPlaces = state.searchCitiesData!.popularPlaces.where((place) {
        return place.name.toLowerCase().contains(lowercaseQuery) ||
               place.city.toLowerCase().contains(lowercaseQuery) ||
               place.country.toLowerCase().contains(lowercaseQuery);
      }).toList();

      state = state.copyWith(
        searchQuery: query,
        filteredCities: filteredCities,
        filteredPopularPlaces: filteredPopularPlaces,
      );
    }
  }

  /// Get current location using centralized location service
  Future<void> getCurrentLocation() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final locationResult = await LocationService.getCurrentLocation();

      if (locationResult.isSuccess) {
        state = state.copyWith(
          currentLocation: locationResult.locationName,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: locationResult.error,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to get current location: $e',
        isLoading: false,
      );
    }
  }

  /// Clear search
  void clearSearch() {
    if (state.searchCitiesData != null) {
      state = state.copyWith(
        searchQuery: '',
        filteredCities: state.searchCitiesData!.cities,
        filteredPopularPlaces: state.searchCitiesData!.popularPlaces,
      );
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Load recent searches from SharedPreferences
  Future<void> loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? savedSearches = prefs.getStringList(_recentSearchesKey);
      if (savedSearches != null) {
        state = state.copyWith(recentSearches: savedSearches);
      }
    } catch (e) {
      debugPrint('Error loading recent searches: $e');
    }
  }

  /// Save recent searches to SharedPreferences
  Future<void> _saveRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_recentSearchesKey, state.recentSearches);
    } catch (e) {
      debugPrint('Error saving recent searches: $e');
    }
  }

  /// Add a search to recent searches
  Future<void> addToRecentSearches(String searchTerm) async {
    if (searchTerm.trim().isEmpty) return;

    List<String> updatedSearches = List.from(state.recentSearches);
    
    // Remove if already exists to avoid duplicates
    updatedSearches.remove(searchTerm);

    // Add to the beginning of the list
    updatedSearches.insert(0, searchTerm);

    // Keep only the last 8 searches
    if (updatedSearches.length > 8) {
      updatedSearches = updatedSearches.take(8).toList();
    }

    state = state.copyWith(recentSearches: updatedSearches);
    
    // Save to SharedPreferences
    await _saveRecentSearches();
  }

  /// Clear recent searches
  Future<void> clearRecentSearches() async {
    state = state.copyWith(recentSearches: []);
    await _saveRecentSearches();
  }

  /// Refresh data
  Future<void> refreshData() async {
    await loadSearchCitiesData();
  }
}

/// Search cities provider
final searchCitiesProvider = StateNotifierProvider<SearchCitiesNotifier, SearchCitiesState>(
  (ref) => SearchCitiesNotifier(),
);
