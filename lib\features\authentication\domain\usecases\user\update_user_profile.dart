﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for updating user profile
class UpdateUserProfile implements UseCase<UserEntity, UpdateUserProfileParams> {
  final UserRepository repository;

  UpdateUserProfile(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(UpdateUserProfileParams params) async {
    return await repository.updateUserProfile(
      firstName: params.firstName,
      lastName: params.lastName,
      email: params.email,
      phone: params.phone,
      dateOfBirth: params.dateOfBirth,
      nationality: params.nationality,
      passportNumber: params.passportNumber,
      preferredLanguage: params.preferredLanguage,
      preferredCurrency: params.preferredCurrency,
    );
  }
}

/// Parameters for UpdateUserProfile use case
class UpdateUserProfileParams extends Equatable {
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final DateTime? dateOfBirth;
  final String? nationality;
  final String? passportNumber;
  final String? preferredLanguage;
  final String? preferredCurrency;

  const UpdateUserProfileParams({
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.dateOfBirth,
    this.nationality,
    this.passportNumber,
    this.preferredLanguage,
    this.preferredCurrency,
  });

  @override
  List<Object?> get props => [
    firstName,
    lastName,
    email,
    phone,
    dateOfBirth,
    nationality,
    passportNumber,
    preferredLanguage,
    preferredCurrency,
  ];
}

