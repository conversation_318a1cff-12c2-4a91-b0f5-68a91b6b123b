import 'package:flutter/material.dart';
// Provider imports removed - using Riverpod providers in individual screens
import 'package:kind_ali/features/booking/presentation/pages/booking/booking_screen.dart';
import 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart';
import 'package:kind_ali/features/home/<USER>/pages/dashboard/dashboard_screen.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/hotel_detail_screen.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/hotel_list_screen.dart';
import 'package:kind_ali/features/itinerary/presentation/pages/itinerary/itinerary_screen.dart';
import 'package:kind_ali/features/booking/presentation/pages/payment/payment_screen.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/profile_screen.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/profile_completion_screen.dart';
import 'package:kind_ali/features/onboarding/presentation/pages/progressive_onboarding_screen.dart';
import 'package:kind_ali/shared/presentation/pages/splash/splash_screen.dart';

/// Class that manages all routes in the application
class AppRoutes {
  // Route names as constants
  static const String home = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String dashboard='/dashboard';
  static const String forgotPassword = '/forgot-password';
  static const String otpVerification = '/otp-verification';
  static const String resetPassword = '/reset-password';
  static const String hotelList = '/hotel-list';
  static const String hotelDetail = '/hotel-detail';
  static const String booking = '/booking';
  static const String profile = '/profile';
  static const String profileCompletion = '/profile-completion';
  static const String progressiveOnboarding = '/progressive-onboarding';
  static const String favorites = '/favorites';
  static const String itinerary = '/itinerary';
  static const String payment = '/payment';
  static const String splash = '/splash';

  /// Generate the application routes
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      home: (context) => const HomeScreen(),
      // login: (context) => const LoginScreen(),
      // signup: (context) => const SignupScreen(),
      dashboard:(context)=> const DashboardScreen(),
      // forgotPassword: (context) => const ForgotPasswordScreen(),
      profile: (context) => const ProfileScreen(),
      profileCompletion: (context) => const ProfileCompletionScreen(),
      progressiveOnboarding: (context) => const ProgressiveOnboardingScreen(),
      hotelList: (context) => const HotelListScreen(),
      booking: (context) => const BookingScreen(),

      splash: (context) => const SplashScreen(),
      // Routes that require parameters are handled in generateRoute
    };
  }

  /// Handle routes that require parameters
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      // case otpVerification:
      //   final args = settings.arguments as Map<String, dynamic>;
      //   return MaterialPageRoute(
      //     builder: (context) => OTPVerificationScreen(
      //       email: args['email'],
      //       isFromForgotPassword: args['isFromForgotPassword'],
      //     ),
      //   );

      // case resetPassword:
      //   final args = settings.arguments as Map<String, dynamic>;
      //   return MaterialPageRoute(
      //     builder: (context) => ResetPasswordScreen(
      //       email: args['email'],
      //     ),
      //   );

      case payment:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => PaymentScreen(
            totalAmount: args['totalAmount'],
            hotelName: args['hotelName'],
            roomType: args['roomType'],
            checkInDate: args['checkInDate'],
            checkOutDate: args['checkOutDate'],
            selectedRoomOptions: args['selectedRoomOptions'],
            guestName: args['guestName'],
            guestEmail: args['guestEmail'],
            guestPhone: args['guestPhone'],
            additionalGuests: args['additionalGuests'],
          ),
        );

      case itinerary:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => ItineraryScreen(
            bookingId: args['bookingId'],
            hotelName: args['hotelName'],
            roomType: args['roomType'],
            checkInDate: args['checkInDate'],
            checkOutDate: args['checkOutDate'],
            numberOfGuests: args['numberOfGuests'],
            totalAmount: args['totalAmount'],
            guestName: args['guestName'],
            guestEmail: args['guestEmail'],
            guestPhone: args['guestPhone'],
            selectedRoomOptions: args['selectedRoomOptions'],
            additionalGuests: args['additionalGuests'],
          ),
        );

      case hotelDetail:
        return MaterialPageRoute(
          builder: (context) => const HotelDetailScreen(),
        );

      case profileCompletion:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const ProfileCompletionScreen(),
        );

      case progressiveOnboarding:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => const ProgressiveOnboardingScreen(),
        );

      default:
        return MaterialPageRoute(
          builder: (context) => const Scaffold(
            body: Center(child: Text('Route not found')),
          ),
        );
    }
  }

  /// Navigate to OTP verification screen
  static void navigateToOtpVerification(BuildContext context, String email, bool isFromForgotPassword) {
    Navigator.pushNamed(
      context,
      otpVerification,
      arguments: {
        'email': email,
        'isFromForgotPassword': isFromForgotPassword,
      },
    );
  }

  /// Navigate to reset password screen
  static void navigateToResetPassword(BuildContext context, String email) {
    Navigator.pushNamed(
      context,
      resetPassword,
      arguments: {
        'email': email,
      },
    );
  }

  /// Navigate to payment screen
  static void navigateToPayment(
    BuildContext context, {
    required double totalAmount,
    required String hotelName,
    required String roomType,
    required String checkInDate,
    required String checkOutDate,
    List<SelectedRoomOption>? selectedRoomOptions,
    String? guestName,
    String? guestEmail,
    String? guestPhone,
    List<Map<String, String>>? additionalGuests,
  }) {
    Navigator.pushNamed(
      context,
      payment,
      arguments: {
        'totalAmount': totalAmount,
        'hotelName': hotelName,
        'roomType': roomType,
        'checkInDate': checkInDate,
        'checkOutDate': checkOutDate,
        'selectedRoomOptions': selectedRoomOptions,
        'guestName': guestName,
        'guestEmail': guestEmail,
        'guestPhone': guestPhone,
        'additionalGuests': additionalGuests,
      },
    );
  }

  /// Navigate to itinerary screen
  static void navigateToItinerary(
    BuildContext context, {
    required String bookingId,
    required String hotelName,
    required String roomType,
    required String checkInDate,
    required String checkOutDate,
    required int numberOfGuests,
    required double totalAmount,
    required String guestName,
    required String guestEmail,
    String? guestPhone,
    List<SelectedRoomOption>? selectedRoomOptions,
    List<Map<String, String>>? additionalGuests,
  }) {
    Navigator.pushNamed(
      context,
      itinerary,
      arguments: {
        'bookingId': bookingId,
        'hotelName': hotelName,
        'roomType': roomType,
        'checkInDate': checkInDate,
        'checkOutDate': checkOutDate,
        'numberOfGuests': numberOfGuests,
        'totalAmount': totalAmount,
        'guestName': guestName,
        'guestEmail': guestEmail,
        'guestPhone': guestPhone,
        'selectedRoomOptions': selectedRoomOptions,
        'additionalGuests': additionalGuests,
      },
    );
  }

  /// Navigate to hotel detail screen
  static void navigateToHotelDetail(BuildContext context) {
    Navigator.pushNamed(context, hotelDetail);
  }
}



