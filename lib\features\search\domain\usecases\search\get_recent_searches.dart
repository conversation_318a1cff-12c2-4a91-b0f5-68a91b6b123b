﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../repositories/search_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for getting recent searches
class GetRecentSearches implements UseCaseNoParams<List<String>> {
  final SearchRepository repository;

  GetRecentSearches(this.repository);

  @override
  Future<Either<Failure, List<String>>> call() async {
    return await repository.getRecentSearches();
  }
}

