import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart';

class GuestSelectionWidget extends ConsumerStatefulWidget {
  const GuestSelectionWidget({super.key});

  @override
  ConsumerState<GuestSelectionWidget> createState() => _GuestSelectionWidgetState();
}

class _GuestSelectionWidgetState extends ConsumerState<GuestSelectionWidget> {
  late List<_LocalRoomData> _rooms;
  // Track which rooms are expanded
  late List<bool> _expandedRooms;

  @override
  void initState() {
    super.initState();
    // Initialize rooms from provider or create a new list
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final homeState = ref.read(homeProvider);
      _rooms = homeState.rooms.isNotEmpty
          ? homeState.rooms.map((room) => _LocalRoomData(adults: room.adults, children: room.children)).toList()
          : [_LocalRoomData(adults: 2, children: 0)];

      // Ensure we have at least one room
      if (_rooms.isEmpty) {
        _rooms.add(_LocalRoomData(adults: 2, children: 0));
      }

      // Initialize expanded state - first room is always expanded, others collapsed
      _expandedRooms = List.generate(_rooms.length, (index) => index == 0);

      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'search.travelers_header'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
   IconButton(
                    icon: const Icon(Icons.close,                       color: AppColors.primary,
),
                    onPressed: () => Navigator.pop(context),
                  ),

            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    ...List.generate(_rooms.length, (index) {
                      return _buildCollapsibleRoom(index);
                    }),
                    if (_rooms.length < 5)
                      Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _rooms.add(_LocalRoomData(adults: 1, children: 0));
                              _expandedRooms.add(true); // New room is expanded by default

                              // Collapse other rooms when adding a new one
                              for (int i = 0; i < _expandedRooms.length - 1; i++) {
                                _expandedRooms[i] = false;
                              }
                            });
                          },
                          icon: const Icon(Icons.add, size: 18),
                          label: Text('search.travelers.addRoom'.tr),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(double.infinity, 48),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          // Add apply button
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: ElevatedButton(
              onPressed: () {
                // Convert local room data to HomeNotifier format and update
                final convertedRooms = _rooms.map((room) =>
                  RoomData(adults: room.adults, children: room.children)
                ).toList();
                ref.read(homeProvider.notifier).setRooms(convertedRooms);
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text('common.apply'.tr, style: TextStyle(fontSize: 16, color: AppColors.surface)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsibleRoom(int roomIndex) {
    _LocalRoomData room = _rooms[roomIndex];
    bool isExpanded = _expandedRooms[roomIndex];

    return Container(
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header (always visible)
          InkWell(
            onTap: () {
              setState(() {
                // Toggle the expanded state of this room
                _expandedRooms[roomIndex] = !_expandedRooms[roomIndex];

                // If expanding this room, collapse others (optional)
                if (_expandedRooms[roomIndex]) {
                  for (int i = 0; i < _expandedRooms.length; i++) {
                    if (i != roomIndex) {
                      _expandedRooms[i] = false;
                    }
                  }
                }
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        '${'search.travelers.room'.tr} ${roomIndex + 1}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Summary information when collapsed
                      if (!isExpanded)
                        Text(
                          '${room.adults} ${room.adults > 1 ? 'search.travelers.adults'.tr : 'search.travelers.adult'.tr}'
                          '${room.children > 0 ? ' · ${room.children} ${room.children > 1 ? 'search.travelers.children'.tr : 'search.travelers.child'.tr}' : ''}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                  Row(
                    children: [
                      if (_rooms.length > 1)
                        IconButton(
                          icon: const Icon(Icons.delete_outline, size: 20),
                          onPressed: () {
                            setState(() {
                              _rooms.removeAt(roomIndex);
                              _expandedRooms.removeAt(roomIndex);

                              // If we removed the last room, make sure at least one room is expanded
                              if (!_expandedRooms.contains(true) && _expandedRooms.isNotEmpty) {
                                _expandedRooms[0] = true;
                              }
                            });
                          },
                          color: AppColors.error,
                          tooltip: 'search.travelers.removeRoom'.tr,
                        ),
                      if (_rooms.length > 1)
                        const SizedBox(width: 4),
                      Icon(
                        isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                        color: Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Expanded content (only visible when expanded)
          if (isExpanded)
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  const SizedBox(height: 8),
                  _buildStepperItem(
                    title: 'search.travelers.adults'.tr,
                    value: room.adults,
                    min: 1,
                    max: 8,
                    onChanged: (v) => setState(() => room.adults = v),
                  ),
                  const SizedBox(height: 16),
                  _buildStepperItem(
                    title: 'search.travelers.children'.tr,
                    subtitle: 'search.travelers.children_age_range'.tr,
                    value: room.children,
                    min: 0,
                    max: 2,
                    onChanged: (v) {
                      setState(() {
                        room.children = v;
                        if (v > room.childrenAges.length) {
                          while (room.childrenAges.length < v) {
                            room.childrenAges.add(0);
                          }
                        } else if (v < room.childrenAges.length) {
                          room.childrenAges = room.childrenAges.sublist(0, v);
                        }
                      });
                    },
                  ),
                  if (room.children > 0) ...[
                    const SizedBox(height: 16),
                    Text(
                      'search.travelers.child_ages'.tr,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                   GridView.count(
  crossAxisCount: 2,
  shrinkWrap: true,
  physics: const NeverScrollableScrollPhysics(),
  childAspectRatio: 3,
  mainAxisSpacing: 8,
  crossAxisSpacing: 8,
  children: List.generate(room.children, (index) {
    return DropdownButtonFormField<int>(
      value: room.childrenAges[index],
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        filled: true,
        fillColor: Colors.grey.shade100,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.primary, width: 1.5),
        ),
      ),
      icon: Icon(
        Icons.keyboard_arrow_down,
        color: AppColors.primary,
        size: 22,
      ),
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: Colors.grey.shade800,
      ),
      dropdownColor: Colors.white,
      items: List.generate(13, (age) {
        return DropdownMenuItem(
          value: age,
          child: Text('$age ${age == 1 ? 'search.travelers.year'.tr : 'search.travelers.years'.tr}'),
        );
      }),
      onChanged: (v) {
        if (v != null) {
          setState(() => room.childrenAges[index] = v);
        }
      },
    );
  }),
)
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStepperItem({
    required String title,
    String? subtitle,
    required int value,
    required int min,
    required int max,
    required Function(int) onChanged,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.remove, size: 18),
                onPressed: value > min ? () => onChanged(value - 1) : null,
                color: value > min ? AppColors.primary : Colors.grey,
              ),
              SizedBox(
                width: 30,
                child: Center(
                  child: Text(
                    value.toString(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add, size: 18),
                onPressed: value < max ? () => onChanged(value + 1) : null,
                color: value < max ? AppColors.primary : Colors.grey,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Local mutable room data class for the widget
class _LocalRoomData {
  int adults;
  int children;
  List<int> childrenAges;

  _LocalRoomData({
    required this.adults,
    required this.children,
  }) : childrenAges = List<int>.generate(children, (index) => 0, growable: true);
}


// import 'package:flutter/material.dart';
// import 'package:kind_ali/core/constants/app_colors.dart';

// class GuestSelectionWidget extends StatefulWidget {
//   const GuestSelectionWidget({super.key});

//   @override
//   State<GuestSelectionWidget> createState() => _GuestSelectionWidgetState();
// }

// class _GuestSelectionWidgetState extends State<GuestSelectionWidget> {
//   final List<RoomData> _rooms = [RoomData(adults: 1, children: 0)];

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Text(
//             'Guest Selection',
//             style: TextStyle(
//               fontSize: 20,
//               fontWeight: FontWeight.bold,
//               color: AppColors.text,
//             ),
//           ),
//           const SizedBox(height: 16),
//           Expanded(
//             child: Container(
//               decoration: BoxDecoration(
//                 color: AppColors.background,
//                 borderRadius: BorderRadius.circular(12),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.grey.withOpacity(0.1),
//                     spreadRadius: 2,
//                     blurRadius: 8,
//                     offset: const Offset(0, 2),
//                   ),
//                 ],
//               ),
//               child: Column(
//                 children: [
//                   // Scrollable rooms list
//                   Expanded(
//                     child: SingleChildScrollView(
//                       child: Padding(
//                         padding: const EdgeInsets.symmetric(horizontal: 16.0),
//                         child: Column(
//                           children: [
//                             ...List.generate(_rooms.length, (index) {
//                               return _buildRoomSelection(index);
//                             }),
//                             const SizedBox(height: 16),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),

//                   // Add Another Room Button
//                   if (_rooms.length < 5)
//                     Container(
//                       decoration: BoxDecoration(
//                         border: Border(
//                           top: BorderSide(
//                             color: AppColors.divider,
//                             width: 1,
//                           ),
//                         ),
//                       ),
//                       child: Padding(
//                         padding: const EdgeInsets.all(12.0),
//                         child: SizedBox(
//                           width: double.infinity,
//                           child: OutlinedButton.icon(
//                             onPressed: () {
//                               setState(() {
//                                 _rooms.add(RoomData(adults: 1, children: 0));
//                               });
//                             },
//                             style: OutlinedButton.styleFrom(
//                               foregroundColor: AppColors.primary,
//                               side: BorderSide(color: AppColors.primary),
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(8),
//                               ),
//                               padding: const EdgeInsets.symmetric(vertical: 12),
//                             ),
//                             icon: const Icon(Icons.add, size: 20),
//                             label: const Text('Add another room'),
//                           ),
//                         ),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildRoomSelection(int roomIndex) {
//     RoomData room = _rooms[roomIndex];

//     return Container(
//       margin: const EdgeInsets.only(bottom: 16.0),
//       decoration: BoxDecoration(
//         border: roomIndex > 0
//             ? Border.all(color: AppColors.divider, width: 1.5)
//             : null,
//         borderRadius: BorderRadius.circular(10.0),
//       ),
//       padding: roomIndex > 0
//           ? const EdgeInsets.fromLTRB(16, 12, 16, 16)
//           : const EdgeInsets.fromLTRB(0, 0, 0, 16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Room Header Row with Room Name and Remove option
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Room ${roomIndex + 1}',
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.text,
//                 ),
//               ),
//               if (roomIndex > 0)
//                 InkWell(
//                   onTap: () {
//                     setState(() {
//                       _rooms.removeAt(roomIndex);
//                     });
//                   },
//                   borderRadius: BorderRadius.circular(20),
//                   child: Container(
//                     padding: const EdgeInsets.all(4),
//                     decoration: BoxDecoration(
//                       color: AppColors.surface,
//                       shape: BoxShape.circle,
//                     ),
//                     child: Icon(
//                       Icons.close,
//                       size: 18,
//                       color: AppColors.textLight,
//                     ),
//                   ),
//                 ),
//             ],
//           ),

//           const SizedBox(height: 16),

//           // Adults Section
//           _buildCounterRow(
//             title: 'Adults',
//             value: room.adults,
//             minValue: 1,
//             maxValue: 8,
//             onChanged: (newValue) {
//               setState(() {
//                 _rooms[roomIndex].adults = newValue;
//               });
//             },
//           ),

//           const SizedBox(height: 16),

//           // Children Section
//           _buildCounterRow(
//             title: 'Children',
//             subtitle: 'Ages 0-12',
//             value: room.children,
//             minValue: 0,
//             maxValue: 2,
//             onChanged: (newValue) {
//               setState(() {
//                 // Update the children count
//                 _rooms[roomIndex].children = newValue;

//                 // Adjust age list size based on new children count
//                 if (newValue > room.childrenAges.length) {
//                   // Add new ages
//                   while (room.childrenAges.length < newValue) {
//                     room.childrenAges.add(0);
//                   }
//                 } else if (newValue < room.childrenAges.length) {
//                   // Remove excess ages
//                   room.childrenAges = room.childrenAges.sublist(0, newValue);
//                 }
//               });
//             },
//           ),

//           // Child Age Selection
//           if (room.children > 0)
//             Container(
//               margin: const EdgeInsets.only(top: 16),
//               padding: const EdgeInsets.all(16),
//               decoration: BoxDecoration(
//                 color: AppColors.secondary,
//                 borderRadius: BorderRadius.circular(8),
//                 border: Border.all(color: AppColors.divider),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'Children Ages',
//                     style: TextStyle(
//                       fontSize: 14,
//                       fontWeight: FontWeight.w500,
//                       color: AppColors.background,
//                     ),
//                   ),
//                   const SizedBox(height: 12),
//                   Wrap(
//                     spacing: 12,
//                     runSpacing: 12,
//                     children: List.generate(room.children, (index) {
//                       return _buildAgeDropdown(
//                         roomIndex: roomIndex,
//                         childIndex: index,
//                       );
//                     }),
//                   ),
//                 ],
//               ),
//             ),
//         ],
//       ),
//     );
//   }

//   Widget _buildAgeDropdown({required int roomIndex, required int childIndex}) {
//     return Container(
//       width: 120,
//       padding: const EdgeInsets.symmetric(horizontal: 12),
//       decoration: BoxDecoration(
//         color: AppColors.background,
//         border: Border.all(color: AppColors.divider),
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: DropdownButtonHideUnderline(
//         child: DropdownButton<int>(
//           value: _rooms[roomIndex].childrenAges[childIndex],
//           isExpanded: true,
//           icon: const Icon(Icons.keyboard_arrow_down, size: 20),
//           style: TextStyle(
//             fontSize: 14,
//             color: AppColors.text,
//           ),
//           items: List.generate(13, (age) {
//             return DropdownMenuItem<int>(
//               value: age,
//               child: Text('$age ${age == 1 ? 'year' : 'years'}'),
//             );
//           }),
//           onChanged: (int? newValue) {
//             if (newValue != null) {
//               setState(() {
//                 _rooms[roomIndex].childrenAges[childIndex] = newValue;
//               });
//             }
//           },
//         ),
//       ),
//     );
//   }

//   Widget _buildCounterRow({
//     required String title,
//     String? subtitle,
//     required int value,
//     required int minValue,
//     required int maxValue,
//     required Function(int) onChanged,
//   }) {
//     return Container(
//       padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
//       decoration: BoxDecoration(
//         color: AppColors.secondary,
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           // Title section
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 title,
//                 style: TextStyle(
//                   fontSize: 14,
//                   fontWeight: FontWeight.w500,
//                   color: AppColors.background,
//                 ),
//               ),
//               if (subtitle != null)
//                 Text(
//                   subtitle,
//                   style: TextStyle(
//                     fontSize: 12,
//                     color: AppColors.textLight,
//                   ),
//                 ),
//             ],
//           ),

//           // Counter section
//           Container(
//             decoration: BoxDecoration(
//               color: AppColors.background,
//               borderRadius: BorderRadius.circular(20),
//               border: Border.all(color: AppColors.divider),
//             ),
//             child: Row(
//               children: [
//                 // Decrement Button
//                 _buildCounterButton(
//                   icon: Icons.remove,
//                   isEnabled: value > minValue,
//                   onPressed: () => onChanged(value - 1),
//                 ),

//                 // Value Display
//                 Container(
//                   padding: const EdgeInsets.symmetric(horizontal: 12),
//                   decoration: BoxDecoration(
//                     border: Border(
//                       left: BorderSide(color: AppColors.divider),
//                       right: BorderSide(color: AppColors.divider),
//                     ),
//                   ),
//                   child: Text(
//                     value.toString(),
//                     style: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                 ),

//                 // Increment Button
//                 _buildCounterButton(
//                   icon: Icons.add,
//                   isEnabled: value < maxValue,
//                   onPressed: () => onChanged(value + 1),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildCounterButton({
//     required IconData icon,
//     required bool isEnabled,
//     required VoidCallback onPressed,
//   }) {
//     return IconButton(
//       icon: Icon(
//         icon,
//         size: 18,
//         color: isEnabled ? AppColors.primary : AppColors.textLight,
//       ),
//       onPressed: isEnabled ? onPressed : null,
//       padding: const EdgeInsets.all(8),
//       constraints: const BoxConstraints(),
//       splashRadius: 20,
//     );
//   }
// }

// RoomData class is imported from home_notifier.dart
