import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

/// Centralized location service for the entire app
class LocationService {
  static const String _lastKnownLocationKey = 'last_known_location';
  static const String _lastKnownLatKey = 'last_known_lat';
  static const String _lastKnownLngKey = 'last_known_lng';
  static const String _locationCacheTimeKey = 'location_cache_time';
  static const int _cacheValidityMinutes = 10; // Cache location for 10 minutes

  /// Get current location with caching and error handling
  static Future<LocationResult> getCurrentLocation({
    bool useCache = true,
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    try {
      // Check cache first if enabled
      if (useCache) {
        final cachedResult = await _getCachedLocation();
        if (cachedResult != null) {
          return cachedResult;
        }
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationResult.error(
          'Location services are disabled. Please enable location services in your device settings.',
          LocationErrorType.serviceDisabled,
        );
      }

      // Check and request permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return LocationResult.error(
            'Location permissions are denied. Please allow location access to find nearby hotels.',
            LocationErrorType.permissionDenied,
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationResult.error(
          'Location permissions are permanently denied. Please enable location access in your device settings.',
          LocationErrorType.permissionDeniedForever,
        );
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: const Duration(seconds: 15), // Timeout after 15 seconds
      );

      // Get address from coordinates
      String locationName = await _getLocationName(
        position.latitude,
        position.longitude,
      );

      final result = LocationResult.success(
        latitude: position.latitude,
        longitude: position.longitude,
        locationName: locationName,
        accuracy: position.accuracy,
        timestamp: DateTime.now(),
      );

      // Cache the result
      await _cacheLocation(result);

      return result;
    } catch (e) {
      debugPrint('Error getting current location: $e');
      
      // Try to return cached location as fallback
      final cachedResult = await _getCachedLocation();
      if (cachedResult != null) {
        return cachedResult.copyWith(
          isFromCache: true,
          error: 'Using cached location. Current location unavailable: $e',
        );
      }

      return LocationResult.error(
        'Failed to get current location: $e',
        LocationErrorType.unknown,
      );
    }
  }

  /// Get location name from coordinates
  static Future<String> _getLocationName(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        
        // Try different combinations to get the best location name
        if (place.locality != null && place.locality!.isNotEmpty) {
          return place.locality!;
        } else if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          return place.administrativeArea!;
        } else if (place.subAdministrativeArea != null && place.subAdministrativeArea!.isNotEmpty) {
          return place.subAdministrativeArea!;
        } else if (place.country != null && place.country!.isNotEmpty) {
          return place.country!;
        }
      }
    } catch (e) {
      debugPrint('Error getting location name: $e');
    }
    
    return 'Current Location';
  }

  /// Cache location data
  static Future<void> _cacheLocation(LocationResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastKnownLocationKey, result.locationName);
      await prefs.setDouble(_lastKnownLatKey, result.latitude);
      await prefs.setDouble(_lastKnownLngKey, result.longitude);
      await prefs.setInt(_locationCacheTimeKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('Error caching location: $e');
    }
  }

  /// Get cached location if valid
  static Future<LocationResult?> _getCachedLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final cacheTime = prefs.getInt(_locationCacheTimeKey);
      if (cacheTime == null) return null;
      
      final cacheDateTime = DateTime.fromMillisecondsSinceEpoch(cacheTime);
      final now = DateTime.now();
      
      // Check if cache is still valid
      if (now.difference(cacheDateTime).inMinutes > _cacheValidityMinutes) {
        return null;
      }
      
      final locationName = prefs.getString(_lastKnownLocationKey);
      final latitude = prefs.getDouble(_lastKnownLatKey);
      final longitude = prefs.getDouble(_lastKnownLngKey);
      
      if (locationName != null && latitude != null && longitude != null) {
        return LocationResult.success(
          latitude: latitude,
          longitude: longitude,
          locationName: locationName,
          accuracy: 0.0,
          timestamp: cacheDateTime,
          isFromCache: true,
        );
      }
    } catch (e) {
      debugPrint('Error getting cached location: $e');
    }
    
    return null;
  }

  /// Clear cached location
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastKnownLocationKey);
      await prefs.remove(_lastKnownLatKey);
      await prefs.remove(_lastKnownLngKey);
      await prefs.remove(_locationCacheTimeKey);
    } catch (e) {
      debugPrint('Error clearing location cache: $e');
    }
  }

  /// Check if location permissions are granted
  static Future<bool> hasLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always || 
             permission == LocationPermission.whileInUse;
    } catch (e) {
      return false;
    }
  }

  /// Open device location settings
  static Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      debugPrint('Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings for permissions
  static Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(
    double lat1, double lon1,
    double lat2, double lon2,
  ) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000; // Convert to km
  }
}

/// Location result class
class LocationResult {
  final double latitude;
  final double longitude;
  final String locationName;
  final double accuracy;
  final DateTime timestamp;
  final bool isSuccess;
  final String? error;
  final LocationErrorType? errorType;
  final bool isFromCache;

  const LocationResult({
    required this.latitude,
    required this.longitude,
    required this.locationName,
    required this.accuracy,
    required this.timestamp,
    required this.isSuccess,
    this.error,
    this.errorType,
    this.isFromCache = false,
  });

  factory LocationResult.success({
    required double latitude,
    required double longitude,
    required String locationName,
    required double accuracy,
    required DateTime timestamp,
    bool isFromCache = false,
  }) {
    return LocationResult(
      latitude: latitude,
      longitude: longitude,
      locationName: locationName,
      accuracy: accuracy,
      timestamp: timestamp,
      isSuccess: true,
      isFromCache: isFromCache,
    );
  }

  factory LocationResult.error(String error, LocationErrorType errorType) {
    return LocationResult(
      latitude: 0.0,
      longitude: 0.0,
      locationName: '',
      accuracy: 0.0,
      timestamp: DateTime.now(),
      isSuccess: false,
      error: error,
      errorType: errorType,
    );
  }

  LocationResult copyWith({
    double? latitude,
    double? longitude,
    String? locationName,
    double? accuracy,
    DateTime? timestamp,
    bool? isSuccess,
    String? error,
    LocationErrorType? errorType,
    bool? isFromCache,
  }) {
    return LocationResult(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      accuracy: accuracy ?? this.accuracy,
      timestamp: timestamp ?? this.timestamp,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error ?? this.error,
      errorType: errorType ?? this.errorType,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }
}

/// Location error types
enum LocationErrorType {
  serviceDisabled,
  permissionDenied,
  permissionDeniedForever,
  timeout,
  unknown,
}
