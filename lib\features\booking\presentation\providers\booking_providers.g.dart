// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'booking_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bookingLocalDataSourceHash() =>
    r'73fe1f6faecaece123eab9e17d5c60c3dcb99920';

/// See also [bookingLocalDataSource].
@ProviderFor(bookingLocalDataSource)
final bookingLocalDataSourceProvider =
    AutoDisposeFutureProvider<BookingLocalDataSource>.internal(
  bookingLocalDataSource,
  name: r'bookingLocalDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bookingLocalDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BookingLocalDataSourceRef
    = AutoDisposeFutureProviderRef<BookingLocalDataSource>;
String _$bookingRepositoryHash() => r'8ecdbea2b5aa96c09b7dbdc9def6e51061d76d0a';

/// See also [bookingRepository].
@ProviderFor(bookingRepository)
final bookingRepositoryProvider =
    AutoDisposeFutureProvider<BookingRepository>.internal(
  bookingRepository,
  name: r'bookingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bookingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BookingRepositoryRef = AutoDisposeFutureProviderRef<BookingRepository>;
String _$checkRoomAvailabilityHash() =>
    r'99f72c3687610601b54eae8b6abacfdb3e51db6c';

/// See also [checkRoomAvailability].
@ProviderFor(checkRoomAvailability)
final checkRoomAvailabilityProvider =
    AutoDisposeFutureProvider<CheckRoomAvailability>.internal(
  checkRoomAvailability,
  name: r'checkRoomAvailabilityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$checkRoomAvailabilityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CheckRoomAvailabilityRef
    = AutoDisposeFutureProviderRef<CheckRoomAvailability>;
String _$createBookingHash() => r'bb1f3fdb512e8185795c3cd07d993d14dd8de7e9';

/// See also [createBooking].
@ProviderFor(createBooking)
final createBookingProvider = AutoDisposeFutureProvider<CreateBooking>.internal(
  createBooking,
  name: r'createBookingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$createBookingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CreateBookingRef = AutoDisposeFutureProviderRef<CreateBooking>;
String _$getBookingByIdHash() => r'd99683902102c9e3b2eed602d97a5ed8eb003ec2';

/// See also [getBookingById].
@ProviderFor(getBookingById)
final getBookingByIdProvider =
    AutoDisposeFutureProvider<GetBookingById>.internal(
  getBookingById,
  name: r'getBookingByIdProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getBookingByIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetBookingByIdRef = AutoDisposeFutureProviderRef<GetBookingById>;
String _$getBookingHistoryHash() => r'80ae85ba7904bd575764fe374b57c5893f1f56d6';

/// See also [getBookingHistory].
@ProviderFor(getBookingHistory)
final getBookingHistoryProvider =
    AutoDisposeFutureProvider<GetBookingHistory>.internal(
  getBookingHistory,
  name: r'getBookingHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getBookingHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetBookingHistoryRef = AutoDisposeFutureProviderRef<GetBookingHistory>;
String _$getUpcomingBookingsHash() =>
    r'292e02179529e2c443f12214815221ef0a4d309f';

/// See also [getUpcomingBookings].
@ProviderFor(getUpcomingBookings)
final getUpcomingBookingsProvider =
    AutoDisposeFutureProvider<GetUpcomingBookings>.internal(
  getUpcomingBookings,
  name: r'getUpcomingBookingsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getUpcomingBookingsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetUpcomingBookingsRef
    = AutoDisposeFutureProviderRef<GetUpcomingBookings>;
String _$cancelBookingHash() => r'320a22e0862d51762cb99afd25303c3e4c732047';

/// See also [cancelBooking].
@ProviderFor(cancelBooking)
final cancelBookingProvider = AutoDisposeFutureProvider<CancelBooking>.internal(
  cancelBooking,
  name: r'cancelBookingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cancelBookingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CancelBookingRef = AutoDisposeFutureProviderRef<CancelBooking>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
