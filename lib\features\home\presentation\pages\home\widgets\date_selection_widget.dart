import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart';
import 'package:kind_ali/core/constants/app_colors.dart';

class DateSelectionWidget extends ConsumerStatefulWidget {
  const DateSelectionWidget({super.key});

  @override
  ConsumerState<DateSelectionWidget> createState() => _DateSelectionWidgetState();
}

class _DateSelectionWidgetState extends ConsumerState<DateSelectionWidget> {
  bool isSelectingCheckIn = true;
  final DateFormat dateFormat = DateFormat("d MMM ''yy");

  final DateFormat dayFormat = DateFormat('dd');
  final DateFormat monthYearFormat = DateFormat('MMMM yyyy');
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Set default dates if they're not already set
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final homeState = ref.read(homeProvider);
      if (homeState.checkInDate == null || homeState.checkOutDate == null) {
        // Set default check-in date to today
        ref.read(homeProvider.notifier).setCheckInDate(DateTime.now());
        // Set default check-out date to tomorrow
        ref.read(homeProvider.notifier).setCheckOutDate(DateTime.now().add(const Duration(days: 1)));
      }

      // Scroll to the month of the selected date or current month
      DateTime targetDate = isSelectingCheckIn && homeState.checkInDate != null
          ? homeState.checkInDate!
          : !isSelectingCheckIn && homeState.checkOutDate != null
              ? homeState.checkOutDate!
              : DateTime.now();

      // Calculate approximate position to scroll to
      int monthsFromCurrent = (targetDate.year - DateTime.now().year) * 12 +
                              (targetDate.month - DateTime.now().month);

      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          monthsFromCurrent * 300.0, // Approximate height of a month view
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _selectDate(DateTime date, HomeState homeState) {
    if (isSelectingCheckIn) {
      // Selecting check-in date
      ref.read(homeProvider.notifier).setCheckInDate(date);

      // If new check-in date is after current check-out date or check-out not set,
      // set check-out to the day after check-in
      if (homeState.checkOutDate == null ||
          date.isAfter(homeState.checkOutDate!) ||
          isSameDay(date, homeState.checkOutDate!)) {
        ref.read(homeProvider.notifier).setCheckOutDate(
          DateTime(date.year, date.month, date.day + 1)
        );
      }

      // Move to selecting check-out
      setState(() {
        isSelectingCheckIn = false;
      });
    } else {
      // Selecting check-out date
      if (date.isBefore(homeState.checkInDate!) || isSameDay(date, homeState.checkInDate!)) {
        // If selected date is before or same as check-in, swap them
        DateTime tempDate = homeState.checkInDate!;
        ref.read(homeProvider.notifier).setCheckInDate(date);
        ref.read(homeProvider.notifier).setCheckOutDate(
          DateTime(tempDate.year, tempDate.month, tempDate.day)
        );
      } else {
        ref.read(homeProvider.notifier).setCheckOutDate(date);
      }

      // Stay in check-out selection mode
      setState(() {
        isSelectingCheckIn = false;
      });
    }
  }
  
  // Helper to check if two dates are the same day
  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && 
           date1.month == date2.month && 
           date1.day == date2.day;
  }

  bool _isDateInRange(DateTime date, HomeState homeState) {
    if (homeState.checkInDate == null || homeState.checkOutDate == null) return false;
    return date.isAfter(homeState.checkInDate!) &&
           date.isBefore(homeState.checkOutDate!);
  }

  bool _isCheckInDate(DateTime date, HomeState homeState) {
    if (homeState.checkInDate == null) return false;
    return isSameDay(date, homeState.checkInDate!);
  }

  bool _isCheckOutDate(DateTime date, HomeState homeState) {
    if (homeState.checkOutDate == null) return false;
    return isSameDay(date, homeState.checkOutDate!);
  }

  bool _isSelectable(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return !date.isBefore(today);
  }

  Widget _buildMonthCalendar(DateTime month, HomeState homeState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            monthYearFormat.format(month),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        // Days of week header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const ['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day) {
            return SizedBox(
              width: 40,
              child: Center(
                child: Text(
                  day,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                                 color: AppColors.primary,

                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        // Calendar grid
        _buildMonthDays(month, homeState),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildMonthDays(DateTime month, HomeState homeState) {
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final firstWeekdayOfMonth = firstDayOfMonth.weekday; // 1-7 (Monday-Sunday)
    
    // Adjust to Sunday-based index (0-6)
    final firstDayIndex = firstWeekdayOfMonth % 7;
    
    // Calculate rows needed (including partial first and last week)
    final numRows = ((daysInMonth + firstDayIndex) / 7).ceil();
    
    return Column(
      children: List.generate(numRows, (rowIndex) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(7, (colIndex) {
            final dayIndex = rowIndex * 7 + colIndex - firstDayIndex;
            if (dayIndex < 0 || dayIndex >= daysInMonth) {
              // Empty cell
              return const SizedBox(width: 40, height: 40);
            }
            
            final date = DateTime(month.year, month.month, dayIndex + 1);
            final isSelectable = _isSelectable(date);
            final isCheckIn = _isCheckInDate(date, homeState);
            final isCheckOut = _isCheckOutDate(date, homeState);
            final isInRange = _isDateInRange(date, homeState);
            
            return GestureDetector(
              onTap: () {
                if (isSelectable) {
                  _selectDate(date, homeState);
                  setState(() {}); // Refresh the UI
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isCheckIn || isCheckOut
                      ? AppColors.primary
                      : isInRange
                          ? AppColors.primary.withOpacity(0.2)
                          : Colors.transparent,
                  borderRadius: (isCheckIn || isCheckOut) && !isInRange
                      ? null  // No borderRadius when shape is circle
                      : isCheckIn
                          ? const BorderRadius.horizontal(left: Radius.circular(20))
                          : isCheckOut
                              ? const BorderRadius.horizontal(right: Radius.circular(20))
                              : isInRange
                                  ? BorderRadius.zero
                                  : BorderRadius.circular(20),
                  shape: (isCheckIn || isCheckOut) && !isInRange
                      ? BoxShape.circle
                      : BoxShape.rectangle,
                ),
                alignment: Alignment.center,
                child: Text(
                  dayFormat.format(date),
                  style: TextStyle(
                    color: isCheckIn || isCheckOut
                        ? Colors.white
                        : !isSelectable
                            ? Colors.grey.shade400
                            : isInRange
                                ? AppColors.primary
                                : AppColors.primary,
                    fontWeight: isCheckIn || isCheckOut ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    final homeState = ref.watch(homeProvider);

    return Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Dates',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close,                       color: AppColors.primary,
),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Date selection section
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          isSelectingCheckIn = true;
                          
                          // Scroll to check-in date month
                          if (homeState.checkInDate != null && _scrollController.hasClients) {
                            int monthsFromCurrent = (homeState.checkInDate!.year - DateTime.now().year) * 12 +
                                                   (homeState.checkInDate!.month - DateTime.now().month);
                            
                            _scrollController.animateTo(
                              monthsFromCurrent * 300.0, 
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                                              color: AppColors.white,

                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelectingCheckIn ? AppColors.primary : Colors.grey.shade600,
                            width: isSelectingCheckIn ? 2.0 : 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CHECK-IN',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color:isSelectingCheckIn ? AppColors.primary: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              homeState.checkInDate != null
                                  ? dateFormat.format(homeState.checkInDate!)
                                  : 'Select date',
                              style: TextStyle(
                                fontSize: 16,
                                color:  isSelectingCheckIn? AppColors.primary: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {                        
                        setState(() {
                          // If check-in not selected yet, set it to today first
                          if (homeState.checkInDate == null) {
                            ref.read(homeProvider.notifier).setCheckInDate(DateTime.now());
                          }

                          isSelectingCheckIn = false;

                          // Scroll to check-out date month if selected, otherwise check-in date month
                          DateTime targetDate = homeState.checkOutDate ?? homeState.checkInDate!;
                          if (_scrollController.hasClients) {
                            int monthsFromCurrent = (targetDate.year - DateTime.now().year) * 12 + 
                                                   (targetDate.month - DateTime.now().month);
                            
                            _scrollController.animateTo(
                              monthsFromCurrent * 300.0, 
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: !isSelectingCheckIn ? AppColors.primary : Colors.grey.shade600,
                            width: !isSelectingCheckIn ? 2.0 : 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CHECK-OUT',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color:!isSelectingCheckIn?AppColors.primary: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              homeState.checkOutDate != null
                                  ? dateFormat.format(homeState.checkOutDate!)
                                  : 'Select date',
                              style: TextStyle(
                                fontSize: 16,
                                                               color:!isSelectingCheckIn?AppColors.primary: Colors.grey.shade600,

                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Show calendar
              Flexible(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    children: List.generate(12, (monthIndex) {
                      final currentDate = DateTime.now();
                      final month = DateTime(currentDate.year, currentDate.month + monthIndex);
                      return _buildMonthCalendar(month, homeState);
                    }),
                  ),
                ),
              ),
              // Show selected nights if both dates selected
              if (homeState.checkInDate != null && homeState.checkOutDate != null) ...[
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.primary),
                  ),
                  child: Text(
                    '${homeState.checkOutDate!.difference(homeState.checkInDate!).inDays} nights selected',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Done button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      'Apply',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
  }
}