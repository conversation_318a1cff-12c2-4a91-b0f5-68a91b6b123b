import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/services/location_service.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';

/// Dialog to handle location permission requests and settings
class LocationPermissionDialog extends StatelessWidget {
  final LocationErrorType errorType;
  final String message;
  final VoidCallback? onRetry;

  const LocationPermissionDialog({
    super.key,
    required this.errorType,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            _getIconForErrorType(),
            color: _getColorForErrorType(),
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getTitleForErrorType(),
              style: AppTextStyles.headline2.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: AppTextStyles.bodyText1.copyWith(
              color: AppColors.textLight,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          if (errorType == LocationErrorType.serviceDisabled ||
              errorType == LocationErrorType.permissionDeniedForever)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getHelpTextForErrorType(),
                      style: AppTextStyles.bodyText2.copyWith(
                        color: AppColors.primary,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: AppColors.textLight,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (errorType == LocationErrorType.permissionDenied && onRetry != null)
          CustombuttonWidget(
            text: 'Try Again',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 8,
            height: 40,
            width: 100,
            onPressed: () {
              Navigator.of(context).pop();
              onRetry?.call();
            },
          ),
        if (errorType == LocationErrorType.serviceDisabled)
          CustombuttonWidget(
            text: 'Open Settings',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 8,
            height: 40,
            width: 120,
            onPressed: () async {
              Navigator.of(context).pop();
              await LocationService.openLocationSettings();
            },
          ),
        if (errorType == LocationErrorType.permissionDeniedForever)
          CustombuttonWidget(
            text: 'App Settings',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 8,
            height: 40,
            width: 120,
            onPressed: () async {
              Navigator.of(context).pop();
              await LocationService.openAppSettings();
            },
          ),
      ],
    );
  }

  IconData _getIconForErrorType() {
    switch (errorType) {
      case LocationErrorType.serviceDisabled:
        return Icons.location_disabled;
      case LocationErrorType.permissionDenied:
      case LocationErrorType.permissionDeniedForever:
        return Icons.location_off;
      case LocationErrorType.timeout:
        return Icons.access_time;
      case LocationErrorType.unknown:
      default:
        return Icons.error_outline;
    }
  }

  Color _getColorForErrorType() {
    switch (errorType) {
      case LocationErrorType.serviceDisabled:
      case LocationErrorType.permissionDenied:
      case LocationErrorType.permissionDeniedForever:
        return Colors.orange;
      case LocationErrorType.timeout:
        return Colors.blue;
      case LocationErrorType.unknown:
      default:
        return Colors.red;
    }
  }

  String _getTitleForErrorType() {
    switch (errorType) {
      case LocationErrorType.serviceDisabled:
        return 'Location Services Disabled';
      case LocationErrorType.permissionDenied:
        return 'Location Permission Required';
      case LocationErrorType.permissionDeniedForever:
        return 'Location Permission Denied';
      case LocationErrorType.timeout:
        return 'Location Request Timeout';
      case LocationErrorType.unknown:
      default:
        return 'Location Error';
    }
  }

  String _getHelpTextForErrorType() {
    switch (errorType) {
      case LocationErrorType.serviceDisabled:
        return 'Please enable location services in your device settings to find nearby hotels.';
      case LocationErrorType.permissionDeniedForever:
        return 'Please enable location permission in app settings to use location features.';
      default:
        return '';
    }
  }

  /// Show location permission dialog
  static Future<void> show(
    BuildContext context, {
    required LocationErrorType errorType,
    required String message,
    VoidCallback? onRetry,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LocationPermissionDialog(
        errorType: errorType,
        message: message,
        onRetry: onRetry,
      ),
    );
  }
}

/// Location loading widget
class LocationLoadingWidget extends StatelessWidget {
  final String message;

  const LocationLoadingWidget({
    super.key,
    this.message = 'Getting your location...',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: AppTextStyles.bodyText2.copyWith(
                color: AppColors.textLight,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Location status indicator widget
class LocationStatusWidget extends StatelessWidget {
  final String locationName;
  final bool isFromCache;
  final VoidCallback? onRefresh;

  const LocationStatusWidget({
    super.key,
    required this.locationName,
    this.isFromCache = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isFromCache 
            ? Colors.orange.withValues(alpha: 0.1)
            : AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isFromCache 
              ? Colors.orange.withValues(alpha: 0.3)
              : AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isFromCache ? Icons.cached : Icons.my_location,
            size: 16,
            color: isFromCache ? Colors.orange : AppColors.primary,
          ),
          const SizedBox(width: 6),
          Text(
            locationName,
            style: AppTextStyles.bodyText2.copyWith(
              color: isFromCache ? Colors.orange : AppColors.primary,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
          if (onRefresh != null) ...[
            const SizedBox(width: 6),
            GestureDetector(
              onTap: onRefresh,
              child: Icon(
                Icons.refresh,
                size: 16,
                color: isFromCache ? Colors.orange : AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
