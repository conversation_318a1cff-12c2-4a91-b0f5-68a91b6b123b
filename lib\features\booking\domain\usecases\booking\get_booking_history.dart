﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/booking_entity.dart';
import '../../repositories/booking_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for getting booking history
class GetBookingHistory implements UseCaseNoParams<List<BookingEntity>> {
  final BookingRepository repository;

  GetBookingHistory(this.repository);

  @override
  Future<Either<Failure, List<BookingEntity>>> call() async {
    return await repository.getBookingHistory();
  }
}

