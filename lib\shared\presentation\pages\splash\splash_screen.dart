import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/features/home/<USER>/pages/dashboard/dashboard_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: Duration(seconds: 3),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.5,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Interval(0.0, 0.5, curve: Curves.easeIn),
    ));

    // Check authentication status first
    _checkAuthAndNavigate();
  }

  void _checkAuthAndNavigate() {
    final authState = ref.read(authProvider);

    // If auth is still initializing, wait for it to complete
    if (authState.isInitializing) {
      // Schedule a check for the next frame instead of using ref.listen outside build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final currentState = ref.read(authProvider);
          if (!currentState.isInitializing) {
            _handleAuthStatus(currentState);
          } else {
            // If still initializing, check again after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted) {
                _checkAuthAndNavigate();
              }
            });
          }
        }
      });
    } else {
      // Auth already initialized, check status immediately
      _handleAuthStatus(authState);
    }
  }

  void _handleAuthStatus(AuthState authState) {
    if (authState.isLoggedIn) {
      // User is already logged in, check profile completion status
      _checkProfileCompletionAndNavigate();
    } else {
      // User is not logged in, show splash screen animation
      _startSplashAnimation();
    }
  }

  // Check profile completion status and navigate accordingly
  Future<void> _checkProfileCompletionAndNavigate() async {
    try {
      final authNotifier = ref.read(authProvider.notifier);
      final navigationRoute = await authNotifier.getNavigationRoute();

      if (mounted) {
        if (navigationRoute == '/progressive-onboarding') {
          _navigateToProgressiveOnboarding();
        } else {
          _navigateToDashboard();
        }
      }
    } catch (e) {
      // On error, default to dashboard
      if (mounted) {
        _navigateToDashboard();
      }
    }
  }

  void _startSplashAnimation() {
    // Start animation
    _animationController.forward().then((_) {
      // Navigate to dashboard screen after animation completes
      Future.delayed(Duration(milliseconds: 500), () {
        if (mounted) {
          _navigateToDashboard();
        }
      });
    });
  }

  void _navigateToDashboard() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => DashboardScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: Duration(milliseconds: 800),
        ),
      );
    }
  }

  void _navigateToProgressiveOnboarding() {
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/progressive-onboarding');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F5F0), // Light cream background
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo container
                    Container(
                      width: 200,
                      height: 200,
                      child: Image.asset(color: AppColors.primary,AppImages.logo),
                    ),
                 
                    // Loading indicator
               
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
