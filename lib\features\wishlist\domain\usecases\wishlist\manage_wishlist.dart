﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/features/hotel/domain/entities/hotel_entity.dart';
import 'package:kind_ali/features/hotel/domain/repositories/hotel_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for getting wishlist hotels
class GetWishlistHotels implements UseCaseNoParams<List<HotelEntity>> {
  final HotelRepository repository;

  GetWishlistHotels(this.repository);

  @override
  Future<Either<Failure, List<HotelEntity>>> call() async {
    return await repository.getWishlistHotels();
  }
}

/// Use case for adding hotel to wishlist
class AddToWishlist implements UseCase<void, AddToWishlistParams> {
  final HotelRepository repository;

  AddToWishlist(this.repository);

  @override
  Future<Either<Failure, void>> call(AddToWishlistParams params) async {
    return await repository.addToWishlist(params.hotelId);
  }
}

/// Use case for removing hotel from wishlist
class RemoveFromWishlist implements UseCase<void, RemoveFromWishlistParams> {
  final HotelRepository repository;

  RemoveFromWishlist(this.repository);

  @override
  Future<Either<Failure, void>> call(RemoveFromWishlistParams params) async {
    return await repository.removeFromWishlist(params.hotelId);
  }
}

/// Parameters for AddToWishlist use case
class AddToWishlistParams extends Equatable {
  final int hotelId;

  const AddToWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

/// Parameters for RemoveFromWishlist use case
class RemoveFromWishlistParams extends Equatable {
  final int hotelId;

  const RemoveFromWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

/// Parameters for IsHotelInWishlist use case
class IsHotelInWishlistParams extends Equatable {
  final int hotelId;

  const IsHotelInWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

