// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wishlist_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getWishlistHotelsHash() => r'305380ac965d623044de9e01be8926c7accf9ce1';

/// See also [getWishlistHotels].
@ProviderFor(getWishlistHotels)
final getWishlistHotelsProvider =
    AutoDisposeFutureProvider<GetWishlistHotels>.internal(
  getWishlistHotels,
  name: r'getWishlistHotelsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getWishlistHotelsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetWishlistHotelsRef = AutoDisposeFutureProviderRef<GetWishlistHotels>;
String _$addToWishlistHash() => r'f12b0fddd874e938b3e3efe11617a12f4d0b7354';

/// See also [addToWishlist].
@ProviderFor(addToWishlist)
final addToWishlistProvider = AutoDisposeFutureProvider<AddToWishlist>.internal(
  addToWishlist,
  name: r'addToWishlistProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addToWishlistHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddToWishlistRef = AutoDisposeFutureProviderRef<AddToWishlist>;
String _$removeFromWishlistHash() =>
    r'ee9966cc2302859e0233eef82db57e0ca9e11ed0';

/// See also [removeFromWishlist].
@ProviderFor(removeFromWishlist)
final removeFromWishlistProvider =
    AutoDisposeFutureProvider<RemoveFromWishlist>.internal(
  removeFromWishlist,
  name: r'removeFromWishlistProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$removeFromWishlistHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RemoveFromWishlistRef
    = AutoDisposeFutureProviderRef<RemoveFromWishlist>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
