# PowerShell script to fix relative path imports to absolute package imports

# Define the mapping of relative paths to absolute package paths
$relativeMappings = @{
    "import '../../../core/error/failures.dart';" = "import 'package:kind_ali/core/error/failures.dart';"
    "import '../../../core/network/network_info.dart';" = "import 'package:kind_ali/core/network/network_info.dart';"
    "import '../../../../core/error/failures.dart';" = "import 'package:kind_ali/core/error/failures.dart';"
    "import '../base/usecase.dart';" = "import 'package:kind_ali/core/utils/typedef.dart';"
    "import '../../entities/search_entity.dart';" = "import 'package:kind_ali/features/hotel/domain/entities/search_entity.dart';"
    "import '../datasources/local/hotel_local_datasource.dart';" = "import 'package:kind_ali/features/hotel/data/datasources/hotel_local_datasource.dart';"
    "import '../datasources/remote/hotel_remote_datasource.dart';" = "import 'package:kind_ali/features/hotel/data/datasources/hotel_remote_datasource.dart';"
    "import '../entities/search_entity.dart';" = "import 'package:kind_ali/features/hotel/domain/entities/search_entity.dart';"
    "import '../models/search_cities.dart';" = "import 'package:kind_ali/features/search/data/models/search_cities.dart';"
    "import '../../../features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart';" = "import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart';"
}

# Get all Dart files in the lib directory
$dartFiles = Get-ChildItem -Path "lib" -Filter "*.dart" -Recurse

$totalFiles = $dartFiles.Count
$processedFiles = 0
$updatedFiles = 0

Write-Host "Processing $totalFiles Dart files for relative import fixes..."

foreach ($file in $dartFiles) {
    $processedFiles++
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileUpdated = $false
    
    # Apply each mapping
    foreach ($mapping in $relativeMappings.GetEnumerator()) {
        $oldImport = $mapping.Key
        $newImport = $mapping.Value
        
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $fileUpdated = $true
        }
    }
    
    # Write back if changed
    if ($fileUpdated) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $updatedFiles++
        Write-Host "Updated: $($file.FullName)"
    }
    
    # Progress indicator
    if ($processedFiles % 20 -eq 0) {
        Write-Host "Progress: $processedFiles/$totalFiles files processed, $updatedFiles updated"
    }
}

Write-Host ""
Write-Host "=== RELATIVE IMPORT FIX COMPLETE ==="
Write-Host "Total files processed: $totalFiles"
Write-Host "Files updated: $updatedFiles"
Write-Host "Files unchanged: $($totalFiles - $updatedFiles)"
