import 'package:flutter/material.dart';
import 'package:kind_ali/features/profile/presentation/pages/language/widget/language_bottomsheet.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/widgets/currency_bottomsheet.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/profile/presentation/providers/profile_notifier.dart';
import 'package:kind_ali/features/authentication/presentation/pages/authentication/login_bottom_sheet.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/widgets/booking_history_widget.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/widgets/personal_information_widget.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/widgets/upcoming_bookings_widget.dart';
import 'package:kind_ali/features/wishlist/presentation/pages/wishlist/wishlist_screen.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _ProfileScreenContent();
  }
}

class _ProfileScreenContent extends ConsumerStatefulWidget {
  @override
  ConsumerState<_ProfileScreenContent> createState() => _ProfileScreenContentState();
}

class _ProfileScreenContentState extends ConsumerState<_ProfileScreenContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _profileCardAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _profileCardAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();

    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          ref.read(profileProvider.notifier).loadUserProfile();
        } catch (e) {
          // Handle the case where the widget is disposed
          print('Profile screen disposed before loading profile data: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: Text(
          'profile.profile'.tr,
          style: AppTextStyles.headline2.copyWith(color: Colors.white),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () {
              // Navigate to edit profile page
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PersonalInformationWidget(),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.neutralLight.withAlpha(100),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: Builder(
          builder: (context) {
            final authState = ref.watch(authProvider);
            final profileState = ref.watch(profileProvider);

            // Check if user is logged in
            if (!authState.isLoggedIn) {
              // User is not logged in, show login button
              return _buildLoginPrompt();
            }

            // User is logged in, show profile content
            if (profileState.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.accent,
                ),
              );
            }

            if (profileState.error != null) {
              return _buildErrorState(profileState, ref);
            }

                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    backgroundColor: Colors.transparent,
                                    isScrollControlled: true,
                                    builder: (_) => LanguageBottomsheet(),
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(
                                        15), // Half of width/height for equal radius
                                  ),
                                  child: Center(
                                    child: Row(mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.translate_rounded,
                                            color: Colors.white),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        Text(
                                          'settings.language'.tr,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 15,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 8), // Add spacing between containers
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    backgroundColor: Colors.transparent,
                                    isScrollControlled: true,
                                    builder: (_) => CurrencyBottomsheet(),
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(
                                        15), // Half of width/height for equal radius
                                  ),
                                  child: Center(
                                    child: Row(mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.currency_rupee_outlined,
                                            color: Colors.white),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        Text(
                                          'profile.currency'.tr,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildProfileCard(profileState),
                      _buildSectionTitle('profile.accountSettings'.tr),
                      _buildSettingsSection(context),
                      _buildSectionTitle('profile.bookingInformation'.tr),
                      _buildBookingSection(profileState, context),
                      _buildSectionTitle('profile.supportHelp'.tr),
                      _buildSupportSection(context),
                      SizedBox(height: AppDimensions.paddingSizeLarge),
                      _buildLogoutButton(context, ref),
                      SizedBox(height: AppDimensions.paddingSizeLarge * 2),
                    ],
                  ),
                );
          },
        ),
      ),
    );
  }

 Widget _buildLoginPrompt() {
  if (!mounted) {
    return const SizedBox.shrink();
  }

  return Center(
    child: Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(32),
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 24,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Minimalist icon container
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.person_outline,
              size: 28,
              color: AppColors.primary,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Title with refined typography
          Text(
            'profile.loginRequired'.tr,
            style: AppTextStyles.headline2.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade900,
              letterSpacing: -0.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Subtitle with muted color
          Text(
            'profile.loginToAccessProfile'.tr,
            style: AppTextStyles.bodyText1.copyWith(
              fontSize: 14,
              color: Colors.grey.shade600,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Clean, modern button
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  isDismissible: true,
                  enableDrag: true,
                  builder: (context) => const LoginBottomSheet(),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ).copyWith(
                overlayColor: WidgetStateProperty.all(
                  Colors.white.withOpacity(0.1),
                ),
              ),
              child: Text(
                'auth.login'.tr,
                style: AppTextStyles.subtitle1.copyWith(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

 Future<Map<String, String?>> _getUserProfile(AuthState authState) async {
    // Return user profile data from auth state or empty map
    return {
      'name': null, // AuthState doesn't have currentUserName, will fallback to ProfileState
      'email': authState.currentUserEmail,
    };
  }

 Widget _buildProfileCard(ProfileState profileState) {
  if (!mounted) {
    return const SizedBox.shrink();
  }

  final authState = ref.watch(authProvider);
  return FutureBuilder<Map<String, String?>>(
    future: _getUserProfile(authState),
        builder: (context, snapshot) {
          if (!mounted) {
            return const SizedBox.shrink();
          }

          // Get user data from AuthProvider (real data) or fallback to ProfileProvider (mock data)
          final userData = snapshot.data ?? {};
          final userName = userData['name'] ?? profileState.userProfile.name ?? 'User Name';
          final userEmail = userData['email'] ?? authState.currentUserEmail ?? profileState.userProfile.email ?? '<EMAIL>';

          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, -0.2),
              end: Offset.zero,
            ).animate(_profileCardAnimation),
            child: FadeTransition(
              opacity: _profileCardAnimation,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 24,
                      offset: const Offset(0, 8),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.02),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Profile Avatar
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey.shade100,
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 28,
                        backgroundColor: Colors.grey.shade50,
                        backgroundImage: profileState.userProfile.profileImage != null
                            ? NetworkImage(profileState.userProfile.profileImage!)
                            : null,
                        child: profileState.userProfile.profileImage == null
                            ? Icon(
                                Icons.person_outline,
                                size: 28,
                                color: Colors.grey.shade400,
                              )
                            : null,
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // User Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            userName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                              letterSpacing: -0.2,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            userEmail,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.primary.withAlpha(200),
                              letterSpacing: -0.1,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    // Optional: Status indicator or action button
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.green.shade400,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
}

  // Widget _buildProfileCard(ProfileProvider provider) {
  //   return SlideTransition(
  //     position: Tween<Offset>(
  //       begin: const Offset(0, -0.2),
  //       end: Offset.zero,
  //     ).animate(_profileCardAnimation),
  //     child: FadeTransition(
  //       opacity: _profileCardAnimation,
  //       child: Container(
  //         margin: EdgeInsets.all(AppDimensions.paddingSizeDefault),
  //         padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
  //         decoration: BoxDecoration(
  //           gradient: LinearGradient(
  //             begin: Alignment.topLeft,
  //             end: Alignment.bottomRight,
  //             colors: [
  //               Colors.white.withAlpha(230),
  //               Colors.white.withAlpha(180),
  //             ],
  //           ),
  //           borderRadius: BorderRadius.circular(25),
  //           border: Border.all(
  //             color: Colors.white.withAlpha(100),
  //             width: 1.5,
  //           ),
  //           boxShadow: [
  //             BoxShadow(
  //               color: AppColors.primary.withAlpha(25),
  //               spreadRadius: 0,
  //               blurRadius: 20,
  //               offset: const Offset(0, 10),
  //             ),
  //             BoxShadow(
  //               color: Colors.white.withAlpha(200),
  //               spreadRadius: -5,
  //               blurRadius: 15,
  //               offset: const Offset(0, -5),
  //             ),
  //           ],
  //         ),
  //         child: Column(
  //           children: [
  //             Stack(
  //               children: [
  //                 Container(
  //                   padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
  //                   decoration: BoxDecoration(
  //                     shape: BoxShape.circle,
  //                     gradient: LinearGradient(
  //                       begin: Alignment.topLeft,
  //                       end: Alignment.bottomRight,
  //                       colors: [
  //                         AppColors.primary.withAlpha(200),
  //                         AppColors.secondary.withAlpha(200),
  //                       ],
  //                     ),
  //                     boxShadow: [
  //                       BoxShadow(
  //                         color: AppColors.primary.withAlpha(100),
  //                         blurRadius: 15,
  //                         offset: const Offset(0, 5),
  //                       ),
  //                     ],
  //                   ),
  //                   child: CircleAvatar(
  //                     radius: 50,
  //                     backgroundColor: Colors.white,
  //                     backgroundImage: provider.userProfile.profileImage != null
  //                         ? NetworkImage(provider.userProfile.profileImage!)
  //                         : null,
  //                     child: provider.userProfile.profileImage == null
  //                         ? Icon(
  //                             Icons.person,
  //                             size: 50,
  //                             color: AppColors.primary,
  //                           )
  //                         : null,
  //                   ),
  //                 ),
  //                 Positioned(
  //                   bottom: 0,
  //                   right: 0,
  //                   child: Container(
  //                     padding: const EdgeInsets.all(8),
  //                     decoration: BoxDecoration(
  //                       color: AppColors.accent,
  //                       shape: BoxShape.circle,
  //                       border: Border.all(color: Colors.white, width: 3),
  //                       boxShadow: [
  //                         BoxShadow(
  //                           color: AppColors.accent.withAlpha(100),
  //                           blurRadius: 8,
  //                           offset: const Offset(0, 2),
  //                         ),
  //                       ],
  //                     ),
  //                     child: const Icon(
  //                       Icons.verified,
  //                       color: Colors.white,
  //                       size: 16,
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             SizedBox(height: AppDimensions.paddingSizeDefault),
  //             Text(
  //               provider.userProfile.name ?? 'User Name',
  //               style: AppTextStyles.headline3,
  //             ),
  //             SizedBox(height: AppDimensions.paddingSizeExtraSmall),
  //             Text(
  //               provider.userProfile.email ?? '<EMAIL>',
  //               style: AppTextStyles.bodyText1.copyWith(
  //                 color: AppColors.textLight,
  //               ),
  //             ),
  //             SizedBox(height: AppDimensions.paddingSizeDefault),
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Icon(
  //                   Icons.star,
  //                   color: Colors.amber,
  //                   size: 20,
  //                 ),
  //                 SizedBox(width: 4),
  //                 Text(
  //                   '${provider.userProfile.membershipLevel ?? 'Standard'} ${'profile.member'.tr}',
  //                   style: AppTextStyles.subtitle2.copyWith(
  //                     fontWeight: FontWeight.w600,
  //                     color: AppColors.secondary,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             SizedBox(height: AppDimensions.paddingSizeDefault),
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //               children: [
  //                 _buildProfileStat(
  //                   '${provider.userProfile.totalBookings ?? 0}',
  //                   'profile.bookings'.tr,
  //                   Icons.book_online_rounded,
  //                 ),
  //                 _buildDivider(),
  //                 _buildProfileStat(
  //                   '${provider.userProfile.rewardPoints ?? 0}',
  //                   'profile.points'.tr,
  //                   Icons.card_giftcard_rounded,
  //                 ),
  //                 _buildDivider(),
  //                 _buildProfileStat(
  //                   '${provider.userProfile.completedStays ?? 0}',
  //                   'profile.stays'.tr,
  //                   Icons.home_work_rounded,
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildProfileStat(String value, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 122, 255, 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        SizedBox(height: AppDimensions.paddingSizeExtraSmall),
        Text(
          value,
          style: AppTextStyles.headline1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textLight,
            fontSize: AppDimensions.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 40,
      width: 1,
      color: const Color.fromRGBO(128, 128, 128, 0.2),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        AppDimensions.paddingSizeDefault,
        AppDimensions.paddingSizeLarge,
        AppDimensions.paddingSizeDefault,
        AppDimensions.paddingSizeSmall,
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.accent,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Text(
            title,
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    final settingsItems = [
      {
        'title': 'profile.personalInformation'.tr,
        'icon': Icons.person_outline,
        'iconColor': AppColors.primary,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PersonalInformationWidget(),
            ),
          );
        },
      },

      {
        'title': 'profile.paymentMethods'.tr,
        'icon': Icons.credit_card,
        'iconColor': AppColors.accent,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('profile.paymentMethodsComingSoon'.tr),
              backgroundColor: AppColors.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
            ),
          );
        },
      },
      {
        'title': 'profile.notificationSettings'.tr,
        'icon': Icons.notifications_none_rounded,
        'iconColor': Colors.orange,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('profile.notificationSettingsComingSoon'.tr),
              backgroundColor: AppColors.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
            ),
          );
        },
      },
      {
        'title': 'profile.wishlist'.tr,
        'icon': Icons.favorite_outline,
        'iconColor': Colors.red,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const WishlistScreen(),
            ),
          );
        },
      },
    ];

    return _buildSettingsCards(settingsItems);
  }

  Widget _buildBookingSection(ProfileState profileState, BuildContext context) {
    final bookingItems = [
      {
        'title': 'profile.upcomingBookings'.tr,
        'icon': Icons.upcoming,
        'badge': profileState.userProfile.upcomingBookings?.toString() ?? '0',
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const UpcomingBookingsWidget(),
            ),
          );
        },
      },
      {
        'title': 'profile.bookingHistory'.tr,
        'icon': Icons.history,
        'badge': null,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BookingHistoryWidget(),
            ),
          );
        },
      },
      {
        'title': 'profile.myReviews'.tr,
        'icon': Icons.star_border_rounded,
        'badge': profileState.userProfile.reviewsCount?.toString() ?? '0',
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('profile.myReviewsComingSoon'.tr)),
          );
        },
      },
    ];

    return _buildSettingsCards(bookingItems);
  }

  Widget _buildSupportSection(BuildContext context) {
    final supportItems = [
      {
        'title': 'profile.helpCenter'.tr,
        'icon': Icons.help_outline,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('profile.helpCenterComingSoon'.tr)),
          );
        },
      },
      {
        'title': 'profile.contactUs'.tr,
        'icon': Icons.support_agent,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('profile.contactUsComingSoon'.tr)),
          );
        },
      },
      {
        'title': 'profile.termsConditions'.tr,
        'icon': Icons.description_outlined,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('profile.termsConditionsComingSoon'.tr)),
          );
        },
      },
    ];

    return _buildSettingsCards(supportItems);
  }

  Widget _buildSettingsCards(List<Map<String, dynamic>> items) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        color: AppColors.textLight.withAlpha(25),
        indent: AppDimensions.paddingSizeDefault * 2 +
            24, // Indent to align with text
      ),
      itemBuilder: (context, index) {
        final item = items[index];
        final iconColor = item['iconColor'] as Color? ?? AppColors.primary;
        return ListTile(
          onTap: item['onTap'] as Function(),
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingSizeDefault,
            vertical: AppDimensions.paddingSizeExtraSmall,
          ),
          leading: Icon(
            item['icon'] as IconData,
            color: AppColors.primary,
            size: 24,
          ),
          title: Text(
            item['title'] as String,
            style: AppTextStyles.bodyText1.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (item['badge'] != null)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingSizeSmall,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.accent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    item['badge'] as String,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: AppDimensions.fontSizeExtraSmall,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              SizedBox(
                  width: item['badge'] != null
                      ? AppDimensions.paddingSizeSmall
                      : 0),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textLight,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLogoutButton(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSizeLarge,
      ),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          _showLogoutDialog(context, ref);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.red,
          padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingSizeDefault + 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
            side: BorderSide(color: Colors.red.withAlpha(100), width: 1.5),
          ),
          elevation: 0,
        ).copyWith(
          backgroundColor: WidgetStateProperty.resolveWith<Color>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return Colors.red.withAlpha(25);
              }
              return Colors.transparent;
            },
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.logout_rounded,
              color: Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'profile.logout'.tr,
              style: AppTextStyles.subtitle1.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    final authNotifier = ref.read(authProvider.notifier);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(Icons.logout_rounded, color: Colors.red),
            const SizedBox(width: 12),
            Text('profile.confirmLogout'.tr),
          ],
        ),
        content: Text('profile.logoutMessage'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('profile.cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog first

              // Use auth provider logout
              await authNotifier.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: Text('profile.logout'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ProfileState profileState, WidgetRef ref) {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Text(
              'Error loading profile',
              style: AppTextStyles.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.paddingSizeSmall),
            Text(
              profileState.error!,
              style: AppTextStyles.bodyText2,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingSizeLarge),
            ElevatedButton(
              onPressed: () => ref.read(profileProvider.notifier).loadUserProfile(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accent,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingSizeLarge,
                    vertical: AppDimensions.paddingSizeSmall),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
