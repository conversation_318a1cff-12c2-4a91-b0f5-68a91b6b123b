﻿/// Search providers for Riverpod dependency injection
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/core_providers.dart';
import '../../data/datasources/search_local_datasource.dart';
import '../../data/repositories/search_repository_impl.dart';
import '../../domain/repositories/search_repository.dart';
import '../../domain/usecases/search/get_popular_destinations.dart';
import '../../domain/usecases/search/get_recent_searches.dart';
import '../../domain/usecases/search/add_to_recent_searches.dart';
import '../../domain/usecases/search/get_cities.dart';
import '../../domain/usecases/search/search_cities.dart';

part 'search_providers.g.dart';

// Data Sources
@riverpod
Future<SearchLocalDataSource> searchLocalDataSource(Ref ref) async {
  final sharedPrefs = await ref.watch(sharedPreferencesProvider.future);
  return SearchLocalDataSourceImpl(sharedPreferences: sharedPrefs);
}

// Repository
@riverpod
Future<SearchRepository> searchRepository(Ref ref) async {
  final localDataSource = await ref.watch(searchLocalDataSourceProvider.future);
  final networkInfo = ref.watch(networkInfoProvider);

  return SearchRepositoryImpl(
    localDataSource: localDataSource,
    networkInfo: networkInfo,
  );
}

// Use Cases
@riverpod
Future<GetPopularDestinations> getPopularDestinations(Ref ref) async {
  final repository = await ref.watch(searchRepositoryProvider.future);
  return GetPopularDestinations(repository);
}

@riverpod
Future<GetRecentSearches> getRecentSearches(Ref ref) async {
  final repository = await ref.watch(searchRepositoryProvider.future);
  return GetRecentSearches(repository);
}

@riverpod
Future<AddToRecentSearches> addToRecentSearches(Ref ref) async {
  final repository = await ref.watch(searchRepositoryProvider.future);
  return AddToRecentSearches(repository);
}

@riverpod
Future<GetCities> getCities(Ref ref) async {
  final repository = await ref.watch(searchRepositoryProvider.future);
  return GetCities(repository);
}

@riverpod
Future<SearchCities> searchCities(Ref ref) async {
  final repository = await ref.watch(searchRepositoryProvider.future);
  return SearchCities(repository);
}

