/// API-related constants

class ApiConstants {
  // Base URLs
  static const String baseUrl = 'https://api.kindali.com';
  static const String apiVersion = '/v1';
  static const String fullBaseUrl = '$baseUrl$apiVersion';

  // Authentication endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String sendOtp = '/auth/send-otp';
  static const String verifyOtp = '/auth/verify-otp';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';

  // Hotel endpoints
  static const String hotels = '/hotels';
  static const String hotelDetails = '/hotels/{id}';
  static const String hotelRooms = '/hotels/{id}/rooms';
  static const String hotelAmenities = '/hotels/{id}/amenities';
  static const String searchHotels = '/hotels/search';
  static const String nearbyHotels = '/hotels/nearby';

  // Booking endpoints
  static const String bookings = '/bookings';
  static const String createBooking = '/bookings';
  static const String bookingDetails = '/bookings/{id}';
  static const String cancelBooking = '/bookings/{id}/cancel';
  static const String bookingHistory = '/bookings/history';

  // User endpoints
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/profile';
  static const String wishlist = '/user/wishlist';
  static const String addToWishlist = '/user/wishlist';
  static const String removeFromWishlist = '/user/wishlist/{id}';

  // Payment endpoints
  static const String payment = '/payment';
  static const String paymentMethods = '/payment/methods';
  static const String processPayment = '/payment/process';
  static const String paymentHistory = '/payment/history';

  // Location endpoints
  static const String cities = '/locations/cities';
  static const String countries = '/locations/countries';
  static const String popularDestinations = '/locations/popular';

  // Miscellaneous
  static const String currencies = '/currencies';
  static const String languages = '/languages';
  static const String appConfig = '/config';

  // Request timeouts (in seconds)
  static const int connectionTimeout = 30;
  static const int receiveTimeout = 30;
  static const int sendTimeout = 30;

  // Headers
  static const String contentType = 'Content-Type';
  static const String applicationJson = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  static const String acceptLanguage = 'Accept-Language';
  static const String userAgent = 'User-Agent';
}
