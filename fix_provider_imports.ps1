# Fix provider import paths to point to new feature-based structure
Write-Host "Fixing provider import paths..." -ForegroundColor Cyan

$providerFiles = @(
    "lib\core\providers\hotel_providers.dart",
    "lib\core\providers\search_providers.dart", 
    "lib\core\providers\user_providers.dart"
)

foreach ($file in $providerFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing imports in: $file" -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        # Hotel provider fixes
        if ($file -match "hotel_providers") {
            $content = $content -replace "../../data/datasources/local/hotel_local_datasource.dart", "../../../features/hotel/data/datasources/hotel_local_datasource.dart"
            $content = $content -replace "../../data/datasources/remote/hotel_remote_datasource.dart", "../../../features/hotel/data/datasources/hotel_remote_datasource.dart"
            $content = $content -replace "../../data/repositories/hotel_repository_impl.dart", "../../../features/hotel/data/repositories/hotel_repository_impl.dart"
            $content = $content -replace "../../domain/repositories/hotel_repository.dart", "../../../features/hotel/domain/repositories/hotel_repository.dart"
            $content = $content -replace "../../domain/usecases/hotel/get_hotels.dart", "../../../features/hotel/domain/usecases/hotel/get_hotels.dart"
            $content = $content -replace "../../domain/usecases/hotel/get_featured_hotels.dart", "../../../features/hotel/domain/usecases/hotel/get_featured_hotels.dart"
            $content = $content -replace "../../domain/usecases/hotel/get_hotel_details.dart", "../../../features/hotel/domain/usecases/hotel/get_hotel_details.dart"
            $content = $content -replace "../../domain/usecases/hotel/search_hotels.dart", "../../../features/hotel/domain/usecases/hotel/search_hotels.dart"
            $content = $content -replace "../../domain/usecases/hotel/get_wishlist_hotels.dart", "../../../features/hotel/domain/usecases/hotel/get_wishlist_hotels.dart"
            $content = $content -replace "../../domain/usecases/hotel/add_to_wishlist.dart", "../../../features/hotel/domain/usecases/hotel/add_to_wishlist.dart"
            $content = $content -replace "../../domain/usecases/hotel/remove_from_wishlist.dart", "../../../features/hotel/domain/usecases/hotel/remove_from_wishlist.dart"
        }
        
        # Search provider fixes
        if ($file -match "search_providers") {
            $content = $content -replace "../../data/datasources/local/search_local_datasource.dart", "../../../features/search/data/datasources/search_local_datasource.dart"
            $content = $content -replace "../../data/datasources/remote/search_remote_datasource.dart", "../../../features/search/data/datasources/search_remote_datasource.dart"
            $content = $content -replace "../../data/repositories/search_repository_impl.dart", "../../../features/search/data/repositories/search_repository_impl.dart"
            $content = $content -replace "../../domain/repositories/search_repository.dart", "../../../features/search/domain/repositories/search_repository.dart"
            $content = $content -replace "../../domain/usecases/search/get_popular_destinations.dart", "../../../features/search/domain/usecases/search/get_popular_destinations.dart"
            $content = $content -replace "../../domain/usecases/search/get_recent_searches.dart", "../../../features/search/domain/usecases/search/get_recent_searches.dart"
            $content = $content -replace "../../domain/usecases/search/save_search.dart", "../../../features/search/domain/usecases/search/save_search.dart"
        }
        
        # User provider fixes  
        if ($file -match "user_providers") {
            $content = $content -replace "../../data/datasources/local/user_local_datasource.dart", "../../../features/authentication/data/datasources/auth_local_datasource.dart"
            $content = $content -replace "../../data/datasources/remote/user_remote_datasource.dart", "../../../features/authentication/data/datasources/auth_remote_datasource.dart"
            $content = $content -replace "../../data/repositories/user_repository_impl.dart", "../../../features/authentication/data/repositories/auth_repository_impl.dart"
            $content = $content -replace "../../domain/repositories/user_repository.dart", "../../../features/authentication/domain/repositories/auth_repository.dart"
            $content = $content -replace "../../domain/usecases/user/get_current_user.dart", "../../../features/authentication/domain/usecases/user/get_current_user.dart"
            $content = $content -replace "../../domain/usecases/user/get_user_preferences.dart", "../../../features/authentication/domain/usecases/user/get_user_preferences.dart"
            $content = $content -replace "../../domain/usecases/user/update_user_preferences.dart", "../../../features/authentication/domain/usecases/user/update_user_preferences.dart"
            $content = $content -replace "../../domain/usecases/user/update_user_profile.dart", "../../../features/authentication/domain/usecases/user/update_user_profile.dart"
            $content = $content -replace "../../domain/usecases/user/upload_profile_image.dart", "../../../features/authentication/domain/usecases/user/upload_profile_image.dart"
        }
        
        # Write updated content
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed: $file" -ForegroundColor Green
    }
}

Write-Host "Provider import fixes completed!" -ForegroundColor Yellow
