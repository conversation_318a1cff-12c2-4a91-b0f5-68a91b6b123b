﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../repositories/auth_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for checking if user is logged in
class IsLoggedIn implements UseCaseNoParams<bool> {
  final AuthRepository repository;

  IsLoggedIn(this.repository);

  @override
  Future<Either<Failure, bool>> call() async {
    return await repository.isLoggedIn();
  }
}

