/// Base class for all exceptions in the application
abstract class AppException implements Exception {
  final String message;
  const AppException(this.message);
  
  @override
  String toString() => message;
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException(String message) : super(message);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException([String message = 'Cache error occurred']) : super(message);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException([String message = 'Network error occurred']) : super(message);
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException(String message) : super(message);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException(String message) : super(message);
}

/// File system related exceptions
class FileSystemException extends AppException {
  const FileSystemException(String message) : super(message);
}
