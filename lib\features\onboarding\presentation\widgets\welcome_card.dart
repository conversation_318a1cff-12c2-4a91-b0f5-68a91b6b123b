import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';

class WelcomeCard extends StatefulWidget {
  final VoidCallback onContinue;

  const WelcomeCard({
    super.key,
    required this.onContinue,
  });

  @override
  State<WelcomeCard> createState() => _WelcomeCardState();
}

class _WelcomeCardState extends State<WelcomeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo with scale animation
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: Center(
                    child: Image.asset(
                      AppImages.logo,
                      width: 80,
                      height: 80,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 40),

          // Welcome text with fade animation
          FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                Text(
                  '👋 Welcome to kind_ali!',
                  style: AppTextStyles.headline1.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  'Let\'s set up your profile so you can start exploring amazing hotels and experiences.',
                  style: AppTextStyles.bodyText1.copyWith(
                    fontSize: 16,
                    color: AppColors.textLight,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 60),

          // Features preview with slide animation
          SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  _buildFeatureItem(
                    icon: Icons.hotel,
                    title: 'Discover Hotels',
                    description: 'Find perfect stays worldwide',
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    icon: Icons.favorite,
                    title: 'Save Favorites',
                    description: 'Keep track of places you love',
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    icon: Icons.star,
                    title: 'Earn Rewards',
                    description: 'Get points with every booking',
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 60),

          // Continue button with slide animation
          SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: CustombuttonWidget(
                text: 'Let\'s Get Started! 🚀',
                backgroundColor: AppColors.primary,
                textColor: Colors.white,
                borderRadius: 16,
                height: 56,
                isFullWidth: true,
                onPressed: widget.onContinue,
                textStyle: AppTextStyles.button.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Skip option
          FadeTransition(
            opacity: _fadeAnimation,
            child: TextButton(
              onPressed: () {
                // Could add skip functionality if needed
                widget.onContinue();
              },
              child: Text(
                'This will only take a minute',
                style: TextStyle(
                  color: AppColors.textLight,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.subtitle1.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.text,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodyText2.copyWith(
                  color: AppColors.textLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
