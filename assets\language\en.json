{"dashboard": {"hotels": "Hotels", "flights": "Flights", "ship": "Ship", "visa": "Visa"}, "header": {"helpline": "Helpline", "guestselection": "Guest Selection", "contact": {"phone": "+971 800 KINDALI", "email": "<EMAIL>"}, "nav": {"hotels": "Stays", "holidays": "Holidays", "activities": "Activities", "visa": "Visa", "cruise": "Cruise", "apartments": "Apartments", "resorts": "Resorts", "villas": "Villas", "flights": "Flights", "carRentals": "Car Rentals", "attractions": "Attractions", "airportTaxis": "Airport Taxis"}}, "search": {"clear": "Clear", "destination_header": "Where to?", "recent_searches": "Recent Searches", "popular_destinations": "Popular Destinations", "special_offers": "Special Offers", "tour_packages": "Tour Packages", "destination_placeholder": "Where do you want to stay?", "checkin_header": "Check-in", "checkout_header": "Check-out", "travelers_header": "Travelers", "destination": "Destination", "whereAreYouGoing": "Where are you going?", "selectDate": "Select date", "guest": "Guest", "guests": "Guests", "hotelsIn": "Hotels in", "map": "Map", "errorLoadingHotels": "Error loading hotels", "noHotelsFound": "No hotels found", "tryChangingCriteria": "Try changing your search criteria", "goBack": "Go Back", "retry": "Retry", "search_again": "Search Again", "travelers": {"adults": "Adults", "adult": "Adult", "rooms": "Rooms", "children": "Children", "child": "Child", "room": "Room", "child_ages": "CHILD AGES", "children_age_range": "0-12 Years", "under_one_year": "Under 1 year", "year": "year", "years": "years", "addRoom": "Add Room", "removeRoom": "Remove room", "validation": {"min_adults": "Each room must have at least 1 adult", "specify_child_age": "Please specify age for each child", "min_rooms": "You must have at least one room"}}, "placeholder": "Search for hotels, destinations...", "button": "Search", "modify": "Modify Search", "properties_found": "properties found", "view_mode": {"list": "List", "grid": "Grid"}, "calendar": {"today": "Today", "apply": "Apply Dates", "showPrices": "Show Prices", "yourStay": "Your stay:", "night": "night", "nights": "nights", "selectedDate": "Selected Date", "dateRange": "Date Range", "dayNames": {"sunday": "Su", "monday": "Mo", "tuesday": "Tu", "wednesday": "We", "thursday": "Th", "friday": "Fr", "saturday": "Sa"}}, "validation": {"location_required": "Please enter a location"}, "fetching_location": "Fetching your location...", "current_location": "Current Location", "location_error": {"default": "Unable to retrieve your location", "permission_denied": "Location access denied. Please enable location services.", "unavailable": "Location information is unavailable.", "timeout": "Location request timed out.", "not_supported": "Geolocation is not supported by this browser."}}, "filters": {"title": "Filters", "price": "Price", "rating": "Rating", "amenities": "Amenities", "apply": "Apply Filters", "clear": "Clear All", "filterBy": "Filter by:", "accommodationTypes": "Accommodation Types", "propertyTypes": "Property Types", "propertyRating": "Property rating", "facilities": "Facilities", "stars": "stars", "budget": {"title": "Your budget per (per night)", "range": "₹ {{min}} - ₹ {{max}}+"}, "rooms": {"title": "Bedrooms and bathrooms", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms"}, "viewLess": "View less", "viewMore": "View more"}, "sort": {"title": "Sort by", "options": {"topPicks": "Top picks for long stays", "priceLowest": "Price (lowest first)", "priceHighest": "Price (highest first)", "bestReviewed": "Best reviewed and lowest price", "ratingHighToLow": "Property rating (high to low)", "ratingLowToHigh": "Property rating (low to high)", "distance": "Distance from city centre", "topReviewed": "Top reviewed", "rating": "Rating", "priceHighToLow": "Price: High to Low", "priceLowToHigh": "Price: Low to High", "popularity": "Popularity", "deals": "Deals"}}, "hotel": {"night": "night", "nights": "nights", "reviews": {"noReviewsAvailable": "No reviews available", "basedOn": "Based on", "reviews": "reviews", "excellent": "Excellent", "veryGood": "Very Good", "good": "Good", "fair": "Fair", "poor": "Poor", "quickReview": "Quick Review", "shareQuickThoughts": "Share your quick thoughts...", "tapStarsToRate": "Tap stars to rate • Press Enter or send to post", "guest": "Guest", "india": "India", "justNow": "Just now", "guestUser": "Guest User", "reviewPostedSuccessfully": "Review posted successfully!"}, "amenities": "Amenities", "location": "Location", "rooms": "Rooms", "bookNow": "Book Now", "viewDetails": "View Details", "card": {"noImageFound": "No image found", "showOnMap": "Show on map", "fromCentre": "from centre", "bedroom": "bedroom", "bedrooms": "bedrooms", "livingRoom": "living room", "livingRooms": "living rooms", "bathroom": "bathroom", "bathrooms": "bathrooms", "onlyLeftAtPrice": "Only {{count}} left at this price on our site", "nights": "nights", "adults": "adults", "taxesAndCharges": "taxes and charges", "seeAvailability": "See availability"}, "detail": {"save": "Save", "share": "Share Hotel", "shareWith": "Share", "thisHotel": "this hotel", "withFriends": "with friends", "whatsappNotInstalled": "WhatsApp is not installed on this device", "couldNotOpenWhatsapp": "Could not open WhatsApp", "noEmailApp": "No email app found on this device", "couldNotOpenEmail": "Could not open email app", "hotel": "Hotel", "reserve": "Reserve", "excellent": "Excellent", "ratings": "Ratings", "viewOnMap": "View on map", "location": "Location", "taxes": "taxes", "pernight": "per night", "allPhotos": "All Photos", "roomsPhotos": "Rooms", "exteriorPhotos": "Exterior", "publicAreasPhotos": "Public Areas", "bathroomPhotos": "Bathroom", "diningAreasPhotos": "Dining Areas", "foodBeveragesPhotos": "Food & Beverages", "facilitiesPhotos": "Facilities", "noImagesAvailable": "No images available for this category", "overview": "Overview", "about": "About", "readMore": "Read more", "readLess": "Read less", "popularFacilities": "Popular Facilities", "quickFacts": "Quick Facts", "checkIn": "Check-in", "checkOut": "Check-out", "yearOpened": "Year opened", "numberOfRooms": "Number of rooms", "languagesSpoken": "Languages spoken", "nearbyPlaces": "Nearby Places", "hotelDetails": "Hotel Details", "selectYourRoom": "Select your room", "hotelFacilities": "Hotel Facilities", "policies": "Policies", "navigation": {"overview": "Overview", "rooms": "Rooms", "location": "Location", "reviews": "Reviews", "facilities": "Facilities", "policies": "Policies"}}, "amenitiesrooms": {"air_conditioned_rooms": "Air-conditioned rooms", "bedside_socket": "Bedside socket", "connected_rooms": "Connected rooms", "crockery_items": "Crockery items", "cupboard": "Cupboard", "daily_housekeeping": "Daily housekeeping", "dining_table": "Dining Table", "dining_area_separate": "Dining area (separate)", "dustbins": "<PERSON>bins", "exterior_corridor": "Exterior corridor", "fan": "Fan", "free_drinking_water": "Free drinking water", "ground_floor_available": "Ground floor available", "hair_dryer": "Hair dryer", "iron_for_trousers": "Iron for trousers", "ironing_facilities": "Ironing facilities", "linens": "Linens", "marbled_tiled_floor": "Marbled/tiled floor", "mini_bar": "Mini bar", "newspaper": "Newspaper", "refrigerator": "Refrigerator", "safe_box": "Safe box", "satellite_channels": "Satellite channels", "seating_area": "Seating area", "separate_entrance": "Separate entrance", "smoke_detector": "Smoke detector", "sofa": "So<PERSON>", "staircase_access": "Staircase access", "study_table": "Study table", "swimming_pool": "Swimming pool", "tea_coffee_maker": "Tea/Coffee maker", "telephone": "Telephone", "toiletries": "Toiletries", "towels": "<PERSON><PERSON><PERSON>", "walk_in_shower": "Walk-in shower", "wake_up_call": "Wake-up call", "wifi": "Wi-Fi", "wooden_floor": "Wooden floor", "safety_security_feature": "Safety/security feature"}, "facilities": {"swimmingPool": "Swimming Pool", "beachAccess": "Beach Access", "freeWifi": "Free WiFi", "restaurant": "Restaurant", "bar": "Bar", "spa": "Spa", "roomAmenities": "Room amenities", "airConditioned": "Air-conditioned rooms", "dailyHousekeeping": "Daily housekeeping", "freeDrinkingWater": "Free drinking water", "hotColdWater": "Hot/Cold water", "teaCoffeeMaker": "Tea/Coffee maker", "safetyDepositBox": "Safety Deposit Box", "internetAccess": "Internet Access", "internetServices": "Internet services", "foodAndDrinks": "Food and drinks", "continentalBreakfast": "Continental Breakfast", "roomService24": "24/7 Room Service", "coffeeShop": "Coffee Shop", "inRoomBreakfast": "In-Room Breakfast", "activitiesAndSports": "Activities and sports", "indoorSwimmingPool": "Indoor Swimming Pool", "fitnessCenter": "Fitness Center", "spaSauna": "Spa/Sauna", "bicycleRental": "Bicycle Rental", "servicesAndConveniences": "Services and conveniences", "disabledFacilities": "Facilities for Differently-Abled Guests", "luggageStorage": "Luggage Storage", "laundryService": "Laundry Service", "chapel": "Chapel", "doorman": "<PERSON>man", "dryCleaning": "Dry-Cleaning", "library": "Library", "postalService": "Postal Service", "safetyAndCleanliness": "Safety and cleanliness", "cashlessPayment": "Cashless Payment Service", "commonAreaDisinfection": "Common Area Disinfection (Daily)", "doctorOnCall": "Doctor/Nurse on Call", "freeFaceMasks": "Free Face Masks", "noSharedStationery": "No Shared Stationery", "kitchen": "Kitchen", "bathroom": "Bathroom", "shower": "Shower", "accessibility": "Accessibility", "elevatorAccess": "Elevator access", "transfersAndTransport": "Transfers and transport", "onsiteParking": "Onsite car parking", "airportTransfer": "Airport transfer", "carRentalService": "Car rental service", "shuttleService": "Shuttle service", "taxiCabService": "Taxi/Cab service", "access": "Access", "frontDesk24": "24*7 Front Desk", "nonSmokingRooms": "Non-smoking rooms", "privateCheckInOut": "Private Check-in/out", "safetyAndSecurity": "Safety and security", "security24": "24*7 Security", "cctvCommonAreas": "CCTV in common areas", "fireExtinguisher": "Fire extinguisher", "languagesSpoken": "Languages spoken", "english": "English", "hindi": "Hindi"}}, "footer": {"company": {"title": "Company", "aboutUs": "About Us", "management": "Management", "press": "Press", "others": "Others", "investors": "Investors"}, "productsServices": {"title": "Products & Services", "flights": "Flights", "hotels": "Hotels", "holidays": "Holidays", "bus": "Bus", "forex": "Forex", "cabs": "Cabs"}, "support": {"title": "Support", "contact": "Contact", "blog": "Blog"}, "policies": {"title": "Policies & Terms", "privacyPolicy": "Privacy Policy", "termsOfUse": "Terms of Use", "euGdpr": "EU-GDPR Policy"}, "suppliers": {"title": "For Suppliers and Affiliates", "addHotel": "Add Hotel", "travelAgents": "Travel Agents", "corporates": "Corporates"}, "partners": {"memberOf": "Member Of", "weAccept": "We Accept", "partners": "Partners"}}, "common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "close": "Close", "save": "Save", "cancel": "Cancel", "apply": "Apply", "clear": "Clear", "back": "Back", "next": "Next", "account": "Account", "myAccount": "MY ACCOUNT", "settings": "Settings", "notifications": "Notifications", "noNotifications": "No notifications yet", "markAllAsRead": "Mark all as read", "viewAllNotifications": "View all notifications", "signInRegister": "Sign in / Register", "listYourProperty": "List your property", "suggestedLanguages": "Suggested languages", "allLanguages": "All languages", "search": "Search", "noDataFound": "No data found...", "continue": "Continue", "or": "Or", "email": "Email", "whatsapp": "WhatsApp", "welcomeBack": "Welcome Back", "signInToContinue": "Sign in to continue", "selectCountryCode": "Select Country Code"}, "navigation": {"home": "Home", "offers": "Offers", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Profile"}, "map": {"mapView": "Map View", "closeMap": "Close map", "searchOnMap": "Search on map", "hotelsFound": "hotels found", "noHotelsFound": "No hotels found matching your criteria", "viewDetails": "View Details", "price": "Price", "reviews": "reviews", "noImageAvailable": "No image available", "hotelsIn": "Hotels in", "selectedArea": "Selected Area", "clickToViewFullScreenMap": "Click on the maximize button to view the full-screen map", "hotelsAvailable": "hotels available", "loadingHotelData": "Loading hotel data..."}, "profile": {"bookings": "Bookings", "profile": "Profile", "wishlist": "Wishlist", "reviews": "Reviews", "logout": "Logout", "myProfile": "My Profile", "personalInformation": "Personal Information", "upcomingBookings": "Upcoming Bookings", "bookingHistory": "Booking History", "changeLanguage": "Change Your Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "helpSupport": "Help & Support", "aboutUs": "About Us", "termsConditions": "Terms & Conditions", "privacyPolicy": "Privacy Policy", "editProfile": "Edit Profile", "saveChanges": "Save Changes", "accountSettings": "Account <PERSON><PERSON>", "bookingInformation": "Booking Information", "supportHelp": "Support & Help", "member": "Member", "points": "Points", "stays": "Stays", "paymentMethods": "Payment Methods", "notificationSettings": "Notification Settings", "myReviews": "My Reviews", "helpCenter": "Help Center", "contactUs": "Contact Us", "confirmLogout": "Confirm <PERSON>ut", "logoutMessage": "Are you sure you want to logout?", "cancel": "Cancel", "personalDetails": "Personal Details", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "edit": "Edit", "save": "Save", "nonEditable": "Non-editable", "tapToChangeProfilePicture": "Tap to change profile picture", "enterFullName": "Enter your full name", "enterPhoneNumber": "Enter your phone number", "pleaseEnterName": "Please enter your name", "pleaseEnterPhoneNumber": "Please enter your phone number", "nameUpdatedSuccessfully": "Name updated successfully", "phoneUpdatedSuccessfully": "Phone number updated successfully", "emailCannotBeChanged": "Email address cannot be changed for security reasons", "profilePictureUploadComingSoon": "Profile picture upload feature coming soon", "paymentMethodsComingSoon": "Payment Methods feature coming soon", "completeProfile": "Complete Your Profile", "completeProfileDescription": "Help us personalize your experience by adding your details. You can always update this information later.", "email": "Email", "enterEmail": "Enter your email address", "saveProfile": "Save Profile", "skipForNow": "Skip for now", "profileUpdatedSuccessfully": "Profile updated successfully!", "profileUpdateFailed": "Failed to update profile. Please try again.", "notificationSettingsComingSoon": "Notification Settings feature coming soon", "myReviewsComingSoon": "My Reviews feature coming soon", "helpCenterComingSoon": "Help Center feature coming soon", "contactUsComingSoon": "Contact Us feature coming soon", "termsConditionsComingSoon": "Terms & Conditions feature coming soon", "currencyChangedTo": "Currency changed to", "loginRequired": "<PERSON><PERSON> Required", "loginToAccessProfile": "Please login to access your profile and manage your bookings"}, "settings": {"language": "Language", "pricedisplay": "Price Display", "helpline": "Helpline", "chooseCurrency": "Choose a currency", "currency": "<PERSON><PERSON><PERSON><PERSON>", "priceView": "Price view"}, "itinerary": {"bookingConfirmation": "Booking Confirmation", "bookingId": "Booking ID", "confirmed": "Confirmed", "print": "Print", "downloadPdf": "Download PDF", "stayDetails": "Stay Details", "checkIn": "Check-in", "checkOut": "Check-out", "roomType": "Room Type", "room": "Room", "rooms": "Rooms", "adult": "Adult", "adults": "Adults", "guestDetails": "Guest Details", "primaryGuest": "Primary Guest", "includedAmenities": "Included Amenities", "freeWifi": "Free Wi-Fi", "breakfastIncluded": "Breakfast Included", "poolAccess": "Pool Access", "freeParking": "Free Parking", "spaAccess": "Spa Access", "fitnessCenter": "Fitness Center", "priceDetails": "Price Details", "roomRate": "Room Rate", "nights": "nights", "taxesAndFees": "Taxes & Fees", "resortFee": "Resort Fee", "totalAmount": "Total Amount", "paymentStatus": "Payment Status", "paidInFull": "Paid in Full", "paymentMethod": "Payment Method", "cardEnding": "ending in", "cancellationPolicy": "Cancellation Policy", "freeCancellation": "Free cancellation until", "cancellationFee": "After that, cancellation will incur a fee equivalent to the first night's stay.", "cancelBooking": "Cancel Booking", "needAssistance": "Need Assistance?", "supportMessage": "Our customer support team is available 24/7 to help you with any questions about your booking.", "contactSupport": "Contact Support", "starHotel": "-star hotel"}, "featureHighlights": {"title": "Why Book With KindAli?", "features": {"topRatedHotels": {"description": "Choose from more than 1000 top-rated hotels worldwide.", "priceDetails": "From affordable stays to luxury resorts."}, "verifiedReviews": {"description": "Find hotels with verified guest reviews and high ratings.", "priceDetails": "Book with confidence every time."}, "primeLocations": {"description": "Stay at prime locations close to attractions and transit.", "priceDetails": "Convenient hotel options in every city."}, "exclusiveDiscounts": {"description": "Enjoy exclusive discounts on hotel bookings.", "priceDetails": "Best deals for every budget."}, "secureBooking": {"description": "Safe and secure hotel booking experience.", "priceDetails": "Verified listings with secure payments."}, "flexibleBookings": {"description": "Flexible bookings with free cancellation options.", "priceDetails": "Book now, cancel later hassle-free."}}}, "auth": {"welcomeBack": "Welcome Back", "signInToContinue": "Sign in to continue", "whatsapp": "WhatsApp", "email": "Email", "whatsappNumber": "WhatsApp Number", "enterWhatsappNumber": "Enter your WhatsApp number", "enterEmailAddress": "Enter your email address", "pleaseEnterWhatsappNumber": "Please enter your WhatsApp number", "pleaseEnterValidPhoneNumber": "Please enter a valid phone number", "pleaseEnterEmailAddress": "Please enter your email address", "pleaseEnterValidEmailAddress": "Please enter a valid email address", "continue": "Continue", "or": "Or", "continueWithGoogle": "Continue with Google", "selectCountryCode": "Select Country Code", "otpVerification": "OTP Verification", "enterOtpSent": "Enter the OTP sent to", "resendOtp": "Resend OTP", "verify": "Verify", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "resetPasswordSuccess": "Password reset successfully", "login": "<PERSON><PERSON>"}, "booking": {"selectRoom": "Select Room", "roomsAvailable": "rooms available", "roomOnly": "Room Only", "roomWithBreakfast": "Room with Breakfast", "selectRooms": "Select Rooms", "totalPrice": "Total Price", "reserveRoom": "Reserve Room", "enterYourDetails": "Enter Your Details", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "bookingfor": "Booking for", "bookingnote": "You can book rooms for other guests while keeping your contact details for confirmation", "sprqnote": "We will forward your request to the hotel. Please note that this is subject to availability and based on the hotel policies", "others": "Others", "addGuest": "Add Guest", "guestDetails": "Guest Details", "specialRequests": "Special Requests", "proceedToPayment": "Proceed to Payment", "bookingConfirmation": "Booking Confirmation", "bookingSuccessful": "Booking Successful", "thankYouForBooking": "Thank you for your booking"}, "payment": {"title": "Payment", "paymentDetails": "Payment Details", "paymentMethod": "Payment Method", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cvv": "CVV", "cardHolderName": "Card Holder Name", "payNow": "Pay Now", "paymentSuccessful": "Payment Successful", "paymentFailed": "Payment Failed", "processing": "Processing...", "cardDetails": "Card Details", "creditDebitCard": "Credit/Debit Card", "googlePay": "Google Pay", "applePay": "Apple Pay", "paypal": "PayPal", "saveCardFuture": "Save card for future payments", "securePayment": "Verified listings with secure payments", "bookingConfirmed": "Your booking has been confirmed. You will receive a confirmation email shortly.", "viewItinerary": "View Itinerary", "payAmount": "Pay ₹{amount}", "validation": {"enterCardNumber": "Please enter card number", "cardNumber16Digits": "Card number must be 16 digits", "enterCardHolder": "Please enter card holder name", "enterExpiryDate": "Please enter expiry date", "expiryFormat": "Use format MM/YY", "enterCvv": "Please enter CVV", "cvv3Digits": "CVV must be 3 digits"}, "redirectInfo": {"paypal": "You will be redirected to PayPal to complete your payment securely.", "googlePay": "You will be redirected to Google Pay to complete your payment securely.", "applePay": "You will be redirected to Apple Pay to complete your payment securely.", "secureViaPaypal": "Secure payment via PayPal", "secureViaGooglePay": "Secure payment via Google Pay", "secureViaApplePay": "Secure payment via Apple Pay"}}, "wishlist": {"myWishlist": "My Wishlist", "noWishlistItems": "No items in your wishlist", "addToWishlist": "Add to Wishlist", "removeFromWishlist": "Remove from Wishlist"}}