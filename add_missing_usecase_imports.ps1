# Add missing UseCase imports to all UseCase files
Write-Host "Adding missing UseCase imports..." -ForegroundColor Cyan

$usecaseFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart" | Where-Object {
    $_.FullName -match "usecases" -and $_.FullName -notmatch "base"
}

$fixedFiles = 0

foreach ($file in $usecaseFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check if file is missing UseCase import but has UseCase or UseCaseNoParams
    if ($content -notmatch "shared/domain/usecases/base/usecase.dart" -and 
        ($content -match "implements\s+UseCase" -or $content -match "implements\s+UseCaseNoParams")) {
        
        Write-Host "Adding UseCase import to: $($file.Name)" -ForegroundColor Yellow
        
        $lines = Get-Content $file.FullName
        $newLines = @()
        $importAdded = $false
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            $line = $lines[$i]
            $newLines += $line
            
            # Add UseCase import after the last import
            if ($line -match "^import\s+" -and -not $importAdded) {
                $nextLineIsImport = $false
                if ($i + 1 -lt $lines.Length) {
                    $nextLineIsImport = $lines[$i + 1] -match "^import\s+"
                }
                
                if (-not $nextLineIsImport) {
                    $newLines += "import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';"
                    $importAdded = $true
                }
            }
        }
        
        # Write updated content
        $newLines | Set-Content $file.FullName -Encoding UTF8
        $fixedFiles++
        Write-Host "Fixed: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Added UseCase imports to $fixedFiles files" -ForegroundColor Yellow
