# PowerShell script to fix UseCase base class imports

$dartFiles = Get-ChildItem -Path 'lib' -Filter '*.dart' -Recurse | Where-Object { $_.FullName -like '*usecases*' }
$updatedFiles = 0

Write-Host "Fixing UseCase base class imports..." -ForegroundColor Cyan

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check if file implements UseCase but doesn't import the base class
    if ($content -match "implements\s+(UseCase|UseCaseNoParams)" -and $content -notmatch "shared/domain/usecases/base/usecase.dart") {
        $lines = $content -split [Environment]::NewLine
        $importIndex = -1
        
        # Find the last import line
        for ($i = 0; $i -lt $lines.Length; $i++) {
            if ($lines[$i] -match "^import\s+") {
                $importIndex = $i
            }
        }
        
        if ($importIndex -ge 0) {
            # Insert the UseCase import after the last import
            $newImport = "import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';"
            $newLines = @()
            $newLines += $lines[0..$importIndex]
            $newLines += $newImport
            $newLines += $lines[($importIndex+1)..($lines.Length-1)]
            $content = $newLines -join [Environment]::NewLine
            
            Set-Content -Path $file.FullName -Value $content -NoNewline
            $updatedFiles++
            Write-Host "Added UseCase import to: $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "Updated $updatedFiles files with UseCase imports" -ForegroundColor Yellow
