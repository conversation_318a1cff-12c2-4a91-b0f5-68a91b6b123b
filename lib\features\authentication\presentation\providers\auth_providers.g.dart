// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authLocalDataSourceHash() =>
    r'a13a214532b85bc2e14beaf55a0d34cec9ffb63d';

/// See also [authLocalDataSource].
@ProviderFor(authLocalDataSource)
final authLocalDataSourceProvider =
    AutoDisposeFutureProvider<AuthLocalDataSource>.internal(
  authLocalDataSource,
  name: r'authLocalDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authLocalDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthLocalDataSourceRef
    = AutoDisposeFutureProviderRef<AuthLocalDataSource>;
String _$authRemoteDataSourceHash() =>
    r'ff3b1e962e33cb726f80a02ea51de9750c12720f';

/// See also [authRemoteDataSource].
@ProviderFor(authRemoteDataSource)
final authRemoteDataSourceProvider =
    AutoDisposeProvider<AuthRemoteDataSource>.internal(
  authRemoteDataSource,
  name: r'authRemoteDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authRemoteDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthRemoteDataSourceRef = AutoDisposeProviderRef<AuthRemoteDataSource>;
String _$authRepositoryHash() => r'e4fdacea4954c69234fffd8f4aaefd0bdd639bd4';

/// See also [authRepository].
@ProviderFor(authRepository)
final authRepositoryProvider =
    AutoDisposeFutureProvider<AuthRepository>.internal(
  authRepository,
  name: r'authRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthRepositoryRef = AutoDisposeFutureProviderRef<AuthRepository>;
String _$loginWithEmailHash() => r'bae5783dfd260497fd46970513afa570054d700c';

/// See also [loginWithEmail].
@ProviderFor(loginWithEmail)
final loginWithEmailProvider =
    AutoDisposeFutureProvider<LoginWithEmail>.internal(
  loginWithEmail,
  name: r'loginWithEmailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginWithEmailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LoginWithEmailRef = AutoDisposeFutureProviderRef<LoginWithEmail>;
String _$loginWithPhoneHash() => r'db7192acf03a59fad65341b80ea88c7122246a48';

/// See also [loginWithPhone].
@ProviderFor(loginWithPhone)
final loginWithPhoneProvider =
    AutoDisposeFutureProvider<LoginWithPhone>.internal(
  loginWithPhone,
  name: r'loginWithPhoneProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginWithPhoneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LoginWithPhoneRef = AutoDisposeFutureProviderRef<LoginWithPhone>;
String _$registerWithEmailHash() => r'4c34bd81daf6201f2e77839d250dd62e90bad292';

/// See also [registerWithEmail].
@ProviderFor(registerWithEmail)
final registerWithEmailProvider =
    AutoDisposeFutureProvider<RegisterWithEmail>.internal(
  registerWithEmail,
  name: r'registerWithEmailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$registerWithEmailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RegisterWithEmailRef = AutoDisposeFutureProviderRef<RegisterWithEmail>;
String _$registerWithPhoneHash() => r'b5a3618ca98de1cab0ce6d1e60cd6c53cc0fdfd6';

/// See also [registerWithPhone].
@ProviderFor(registerWithPhone)
final registerWithPhoneProvider =
    AutoDisposeFutureProvider<RegisterWithPhone>.internal(
  registerWithPhone,
  name: r'registerWithPhoneProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$registerWithPhoneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RegisterWithPhoneRef = AutoDisposeFutureProviderRef<RegisterWithPhone>;
String _$getCurrentAuthHash() => r'0911702c2c0db0dd0d48c71ec577ae6fee849d79';

/// See also [getCurrentAuth].
@ProviderFor(getCurrentAuth)
final getCurrentAuthProvider =
    AutoDisposeFutureProvider<GetCurrentAuth>.internal(
  getCurrentAuth,
  name: r'getCurrentAuthProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getCurrentAuthHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetCurrentAuthRef = AutoDisposeFutureProviderRef<GetCurrentAuth>;
String _$isLoggedInHash() => r'd46915358519477d4cab7c1a0da1cd1c91c5f9d0';

/// See also [isLoggedIn].
@ProviderFor(isLoggedIn)
final isLoggedInProvider = AutoDisposeFutureProvider<IsLoggedIn>.internal(
  isLoggedIn,
  name: r'isLoggedInProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isLoggedInHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsLoggedInRef = AutoDisposeFutureProviderRef<IsLoggedIn>;
String _$sendOtpHash() => r'2141ce404f82ee5c7b47d9514f302494dd0c6783';

/// See also [sendOtp].
@ProviderFor(sendOtp)
final sendOtpProvider = AutoDisposeFutureProvider<SendOtp>.internal(
  sendOtp,
  name: r'sendOtpProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$sendOtpHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SendOtpRef = AutoDisposeFutureProviderRef<SendOtp>;
String _$verifyOtpHash() => r'59eb3bc2bfec737a7055acf191f87ad746029a11';

/// See also [verifyOtp].
@ProviderFor(verifyOtp)
final verifyOtpProvider = AutoDisposeFutureProvider<VerifyOtp>.internal(
  verifyOtp,
  name: r'verifyOtpProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$verifyOtpHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VerifyOtpRef = AutoDisposeFutureProviderRef<VerifyOtp>;
String _$logoutHash() => r'e250062a769969fde736e2eff0d898dfc632d9bd';

/// See also [logout].
@ProviderFor(logout)
final logoutProvider = AutoDisposeFutureProvider<Logout>.internal(
  logout,
  name: r'logoutProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$logoutHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LogoutRef = AutoDisposeFutureProviderRef<Logout>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
