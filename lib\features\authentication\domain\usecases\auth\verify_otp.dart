﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../repositories/auth_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for verifying OTP
class VerifyOtp implements UseCase<bool, VerifyOtpParams> {
  final AuthRepository repository;

  VerifyOtp(this.repository);

  @override
  Future<Either<Failure, bool>> call(VerifyOtpParams params) async {
    return await repository.verifyOtp(
      phone: params.phone,
      otp: params.otp,
    );
  }
}

/// Parameters for VerifyOtp use case
class VerifyOtpParams extends Equatable {
  final String phone;
  final String otp;

  const VerifyOtpParams({
    required this.phone,
    required this.otp,
  });

  @override
  List<Object> get props => [phone, otp];
}

