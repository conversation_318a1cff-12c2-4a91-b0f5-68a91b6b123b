﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';

/// Base class for all use cases with parameters
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Base class for use cases without parameters
abstract class UseCaseNoParams<Type> {
  Future<Either<Failure, Type>> call();
}

/// Base class for synchronous use cases with parameters
abstract class SyncUseCase<Type, Params> {
  Either<Failure, Type> call(Params params);
}

/// Base class for synchronous use cases without parameters
abstract class SyncUseCaseNoParams<Type> {
  Either<Failure, Type> call();
}

/// Base class for stream use cases with parameters
abstract class StreamUseCase<Type, Params> {
  Stream<Either<Failure, Type>> call(Params params);
}

/// Base class for stream use cases without parameters
abstract class StreamUseCaseNoParams<Type> {
  Stream<Either<Failure, Type>> call();
}

/// Empty parameters class for use cases that don't need parameters
class NoParams extends Equatable {
  @override
  List<Object> get props => [];
}
