import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/features/profile/presentation/providers/profile_notifier.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/presentation/routes/app_routes.dart';

class ProfileCompletionScreen extends ConsumerStatefulWidget {
  const ProfileCompletionScreen({super.key});

  @override
  ConsumerState<ProfileCompletionScreen> createState() => _ProfileCompletionScreenState();
}

class _ProfileCompletionScreenState extends ConsumerState<ProfileCompletionScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  // Add controllers for text fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load existing profile data after widget initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadExistingProfileData();
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }
  
  // Load existing profile data with smart pre-population
  Future<void> _loadExistingProfileData() async {
    if (!mounted) return;

    try {
      // Get authentication data for smart pre-population
      final authNotifier = ref.read(authProvider.notifier);
      final authState = ref.read(authProvider);
      final storedData = await authNotifier.getStoredProfileData();

      if (mounted) {
        setState(() {
          // Pre-populate name if available
          _nameController.text = storedData['name'] ?? '';

          // Smart pre-population based on login method
          if (authState.loginMethod == 'phone') {
            // User logged in with phone → pre-populate phone, ask for email
            _phoneController.text = authState.currentUserPhone ?? '';
            _emailController.text = storedData['email'] ?? '';
          } else if (authState.loginMethod == 'email') {
            // User logged in with email → pre-populate email, ask for phone
            _emailController.text = authState.currentUserEmail ?? '';
            _phoneController.text = storedData['phone'] ?? '';
          } else {
            // Fallback: populate whatever is available
            _emailController.text = storedData['email'] ?? '';
            _phoneController.text = storedData['phone'] ?? '';
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading profile data: $e');
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      // Show validation error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields correctly'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (!mounted) return;

    try {
      // Show loading state
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Saving profile...'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 1),
        ),
      );

      // Update profile using Riverpod
      await ref.read(profileProvider.notifier).updateProfile(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
      );

      // Save phone number separately to SharedPreferences if provided
      if (_phoneController.text.trim().isNotEmpty) {
        await _savePhoneNumber(_phoneController.text.trim());
      }

      // Save user name to SharedPreferences
      await _saveUserName(_nameController.text.trim());

      // Mark profile as complete
      final authNotifier = ref.read(authProvider.notifier);
      await authNotifier.markProfileAsComplete();

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile completed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Wait a moment for user to see success message
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          // Navigate to dashboard after successful save
          Navigator.pushNamedAndRemoveUntil(
            context,
            AppRoutes.dashboard,
            (route) => false,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving profile: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Save phone number to SharedPreferences
  Future<void> _savePhoneNumber(String phone) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_phone', phone);
    } catch (e) {
      debugPrint('Error saving phone number: $e');
    }
  }

  // Save user name to SharedPreferences
  Future<void> _saveUserName(String name) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_name', name);
    } catch (e) {
      debugPrint('Error saving user name: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope( // Use PopScope instead of deprecated WillPopScope
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;

        final shouldPop = await _confirmExit();
        if (shouldPop == true && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.background,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: AppColors.primary),
            onPressed: () async {
              final shouldPop = await _confirmExit();
              if (shouldPop == true && mounted) {
                Navigator.pop(context);
              }
            },
          ),
        
          centerTitle: true,
        ),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Header section with logo and title
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [
                        // Logo container
                        Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withAlpha(30),
                                blurRadius: 10,
                                spreadRadius: 2,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Image.asset(fit: BoxFit.cover,
                            AppImages.logo,
                            height: 50,
                            width: 120,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Complete Your Profile',
                          style: AppTextStyles.headline2.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Please fill in your details to continue',
                          style: AppTextStyles.bodyText2.copyWith(
                            color: AppColors.textLight,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        // Progress indicator
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildProgressDot(true),
                                  _buildProgressLine(true),
                                  _buildProgressDot(true),
                                  _buildProgressLine(false),
                                  _buildProgressDot(false),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Step 2 of 3',
                                style: AppTextStyles.caption.copyWith(
                                  color: AppColors.textLight,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Form container
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(30),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Name field
                        _buildInputField(
                          controller: _nameController,
                          labelText: 'Full Name',
                          hintText: 'Enter your full name',
                          prefixIcon: Icons.person_outline,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter your name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),

                        // Email field
                        _buildInputField(
                          controller: _emailController,
                          labelText: 'Email Address',
                          hintText: 'Enter your email address',
                          prefixIcon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),

                        // Phone field (optional)
                        _buildInputField(
                          controller: _phoneController,
                          labelText: 'Phone Number (Optional)',
                          hintText: 'Enter your phone number',
                          prefixIcon: Icons.phone_outlined,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            // Phone is optional, so only validate format if provided
                            if (value != null && value.trim().isNotEmpty) {
                              if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
                                return 'Please enter a valid phone number';
                              }
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 32),

                        // Submit button
                        Consumer(
                          builder: (context, ref, child) {
                            final profileState = ref.watch(profileProvider);
                            return CustombuttonWidget(
                              text: 'Save Profile',
                              backgroundColor: AppColors.primary,
                              textColor: Colors.white,
                              borderRadius: 12,
                              height: 56,
                              isFullWidth: true,
                              isLoading: profileState.isLoading,
                              onPressed: profileState.isLoading ? () {} : _saveProfile,
                              textStyle: AppTextStyles.button.copyWith(
                                fontSize: 18,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Additional info text
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.divider,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Your information is secure and will only be used to enhance your booking experience.',
                            style: AppTextStyles.bodyText2.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build progress dot for step indicator
  Widget _buildProgressDot(bool isCompleted) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.primary : AppColors.divider,
        shape: BoxShape.circle,
      ),
    );
  }

  // Build progress line for step indicator
  Widget _buildProgressLine(bool isCompleted) {
    return Container(
      width: 30,
      height: 2,
      color: isCompleted ? AppColors.primary : AppColors.divider,
    );
  }

  // Build custom input field matching app design
  Widget _buildInputField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.divider),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        style: const TextStyle(
          fontSize: 16,
          color: AppColors.text,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          labelStyle: const TextStyle(color: AppColors.textLight),
          hintStyle: TextStyle(color: AppColors.textLight.withAlpha(128)),
          prefixIcon: Icon(
            prefixIcon,
            color: AppColors.primary,
            size: 22,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 20,
          ),
        ),
        validator: validator,
      ),
    );
  }

  Future<bool?> _confirmExit() {
    if (!mounted) return Future.value(false);

    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Discard Changes?',
          style: AppTextStyles.headline3.copyWith(
            color: AppColors.primary,
          ),
        ),
        content: Text(
          'If you go back now, your profile information will not be saved.',
          style: AppTextStyles.bodyText1.copyWith(
            color: AppColors.textLight,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppColors.textLight,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          CustombuttonWidget(
            text: 'Discard',
            backgroundColor: AppColors.error,
            textColor: Colors.white,
            borderRadius: 8,
            height: 40,
            width: 80,
            onPressed: () => Navigator.pop(context, true),
            textStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}