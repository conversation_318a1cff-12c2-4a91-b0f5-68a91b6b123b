﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';
import '../../repositories/search_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for getting popular destinations
class GetPopularDestinations implements UseCase<List<PopularPlaceEntity>, GetPopularDestinationsParams> {
  final SearchRepository repository;

  GetPopularDestinations(this.repository);

  @override
  Future<Either<Failure, List<PopularPlaceEntity>>> call(GetPopularDestinationsParams params) async {
    return await repository.getPopularDestinations();
  }
}

/// Parameters for GetPopularDestinations use case
class GetPopularDestinationsParams extends Equatable {
  final int limit;

  const GetPopularDestinationsParams({this.limit = 10});

  @override
  List<Object> get props => [limit];
}

