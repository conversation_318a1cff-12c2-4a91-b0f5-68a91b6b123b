import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/profile/presentation/providers/profile_notifier.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class PersonalInformationWidget extends ConsumerStatefulWidget {
  const PersonalInformationWidget({Key? key}) : super(key: key);

  @override
  ConsumerState<PersonalInformationWidget> createState() => _PersonalInformationWidgetState();
}

class _PersonalInformationWidgetState extends ConsumerState<PersonalInformationWidget> {
  final _formKey = GlobalKey<FormState>();

  // Text editing controllers
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _currentPasswordController;
  late TextEditingController _newPasswordController;
  late TextEditingController _confirmPasswordController;

  // Editing states
  bool _isEditingName = false;
  bool _isEditingEmail = false;
  bool _isEditingPhone = false;
  bool _isChangingPassword = false;

  // OTP verification
  String? _newEmail;
  bool _isVerifyingOTP = false;
  final TextEditingController _otpController = TextEditingController();

  // Password visibility
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  // Mock phone number (in a real app, this would come from the backend)
  String _phoneNumber = '+91 98765 43210';

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController(text: _phoneNumber);
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();

    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final authState = ref.read(authProvider);
      final profileState = ref.read(profileProvider);

      // Update controllers with loaded data
      setState(() {
        _nameController.text = profileState.userProfile.name ?? '';

        // Pre-populate based on login method
        if (authState.loginMethod == 'email') {
          _emailController.text = authState.currentUserEmail ?? profileState.userProfile.email ?? '';
          _phoneController.text = authState.currentUserPhone ?? '';
        } else if (authState.loginMethod == 'phone') {
          _phoneController.text = authState.currentUserPhone ?? '';
          _emailController.text = profileState.userProfile.email ?? '';
        } else {
          // Fallback to profile provider data
          _emailController.text = profileState.userProfile.email ?? '<EMAIL>';
        }
      });
    });
  }

  @override
  void dispose() {
    // Dispose controllers
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text('profile.personalInformation'.tr),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Builder(
        builder: (context) {
          final profileState = ref.watch(profileProvider);

          if (profileState.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.accent,
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile picture section
                  _buildProfilePictureSection(profileState),

                  SizedBox(height: AppDimensions.paddingSizeLarge),

                  // Personal details section
                  _buildSectionTitle('profile.personalDetails'.tr),
                  SizedBox(height: AppDimensions.paddingSizeDefault),

                  // Name field
                  _buildNameField(),
                  SizedBox(height: AppDimensions.paddingSizeDefault),

                  // Email field (editable with OTP verification)
                  _buildEmailField(),
                  SizedBox(height: AppDimensions.paddingSizeDefault),

                  // Phone number field
                  _buildPhoneField(),
                  SizedBox(height: AppDimensions.paddingSizeLarge),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfilePictureSection(ProfileState profileState) {
    return Center(
      child: Column(
        children: [
          Stack(
            children: [
              // Profile picture
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingSizeExtraSmall),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
                child: CircleAvatar(
                  radius: 60,
                  backgroundColor: const Color.fromRGBO(0, 122, 255, 0.1),
                  backgroundImage: profileState.userProfile.profileImage != null
                      ? NetworkImage(profileState.userProfile.profileImage!)
                      : null,
                  child: profileState.userProfile.profileImage == null
                      ? Icon(
                          Icons.person,
                          size: 60,
                          color: AppColors.primary,
                        )
                      : null,
                ),
              ),

              // Edit button
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      // Show image picker options
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text(
                                'profile.profilePictureUploadComingSoon'.tr)),
                      );
                    },
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingSizeDefault),
          Text(
            'profile.tapToChangeProfilePicture'.tr,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: AppColors.accent,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        SizedBox(width: AppDimensions.paddingSizeSmall),
        Text(
          title,
          style: AppTextStyles.subtitle1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'profile.fullName'.tr,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              if (!_isEditingName)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isEditingName = true;
                    });
                  },
                  child: Text(
                    'profile.edit'.tr,
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingSizeExtraSmall),
          if (_isEditingName)
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: 'profile.enterFullName'.tr,
                border: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: AppColors.primary, width: 2),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'profile.pleaseEnterName'.tr;
                }
                return null;
              },
            )
          else
            Text(
              _nameController.text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (_isEditingName) ...[
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Reset to original value
                    final profileState = ref.read(profileProvider);
                    _nameController.text =
                        profileState.userProfile.name ?? 'User Name';
                    setState(() {
                      _isEditingName = false;
                    });
                  },
                  child: Text(
                    'profile.cancel'.tr,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                SizedBox(width: AppDimensions.paddingSizeSmall),
                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      // Save the name using Riverpod
                      await ref.read(profileProvider.notifier).updateProfile(
                        name: _nameController.text,
                      );

                      if (mounted) {
                        setState(() {
                          _isEditingName = false;
                        });

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text('profile.nameUpdatedSuccessfully'.tr)),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSizeDefault,
                      vertical: AppDimensions.paddingSizeSmall,
                    ),
                  ),
                  child: Text('profile.save'.tr),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'profile.emailAddress'.tr,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              if (!_isEditingEmail && !_isVerifyingOTP)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isEditingEmail = true;
                    });
                  },
                  child: Text(
                    'profile.edit'.tr,
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingSizeExtraSmall),
          if (_isEditingEmail && !_isVerifyingOTP)
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                hintText: 'profile.enterEmail'.tr,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: AppColors.primary, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'profile.pleaseEnterEmail'.tr;
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'profile.pleaseEnterValidEmail'.tr;
                }
                return null;
              },
            )
          else if (_isVerifyingOTP)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Enter OTP sent to $_newEmail',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: AppDimensions.paddingSizeSmall),
                TextFormField(
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  maxLength: 4,
                  decoration: InputDecoration(
                    hintText: 'Enter 4-digit OTP',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    counterText: '',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter OTP';
                    }
                    if (value.length != 4) {
                      return 'OTP must be 4 digits';
                    }
                    return null;
                  },
                ),
              ],
            )
          else
            Text(
              _emailController.text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (!_isEditingEmail && !_isVerifyingOTP)
            SizedBox(height: AppDimensions.paddingSizeExtraSmall)
          else if (_isEditingEmail || _isVerifyingOTP) ...[
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Reset to original value and cancel editing
                    final authState = ref.read(authProvider);
                    _emailController.text = authState.currentUserEmail ?? '';
                    setState(() {
                      _isEditingEmail = false;
                      _isVerifyingOTP = false;
                      _newEmail = null;
                      _otpController.clear();
                    });
                  },
                  child: Text(
                    'profile.cancel'.tr,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                SizedBox(width: AppDimensions.paddingSizeSmall),
                if (_isEditingEmail && !_isVerifyingOTP)
                  ElevatedButton(
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        // Store new email and send OTP
                        setState(() {
                          _newEmail = _emailController.text.trim();
                          _isVerifyingOTP = true;
                          _isEditingEmail = false;
                        });
                        
                        // Simulate OTP sending (local app - no real OTP)
                        await Future.delayed(const Duration(seconds: 1));
                        bool success = true; // Always succeed in local app
                        
                        if (success && mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('OTP sent to $_newEmail'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        } else if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to send OTP'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          // Reset if OTP sending fails
                          setState(() {
                            _isVerifyingOTP = false;
                            _isEditingEmail = true;
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingSizeDefault,
                        vertical: AppDimensions.paddingSizeSmall,
                      ),
                    ),
                    child: Text('Send OTP'),
                  )
                else if (_isVerifyingOTP)
                  ElevatedButton(
                    onPressed: () async {
                      if (_formKey.currentState!.validate() && _newEmail != null) {
                        // Simulate OTP verification (local app - no real OTP)
                        await Future.delayed(const Duration(seconds: 1));
                        bool success = _otpController.text == '1234'; // Simple verification

                        if (success && mounted) {
                          // Update email using Riverpod
                          await ref.read(profileProvider.notifier).updateProfile(email: _newEmail);
                          
                          setState(() {
                            _isVerifyingOTP = false;
                            _emailController.text = _newEmail!;
                            _newEmail = null;
                            _otpController.clear();
                          });
                          
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('profile.emailUpdatedSuccessfully'.tr),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } else if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Invalid OTP'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingSizeDefault,
                        vertical: AppDimensions.paddingSizeSmall,
                      ),
                    ),
                    child: Text('Verify OTP'),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPhoneField() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'profile.phoneNumber'.tr,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              if (!_isEditingPhone)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isEditingPhone = true;
                    });
                  },
                  child: Text(
                    'profile.edit'.tr,
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingSizeExtraSmall),
          if (_isEditingPhone)
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                hintText: 'profile.enterPhoneNumber'.tr,
                border: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                  borderSide: BorderSide(color: AppColors.primary, width: 2),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'profile.pleaseEnterPhoneNumber'.tr;
                }
                return null;
              },
            )
          else
            Text(
              _phoneController.text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (_isEditingPhone) ...[
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Reset to original value
                    _phoneController.text = _phoneNumber;
                    setState(() {
                      _isEditingPhone = false;
                    });
                  },
                  child: Text(
                    'profile.cancel'.tr,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                SizedBox(width: AppDimensions.paddingSizeSmall),
                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      // Phone number is typically managed by auth, but we'll just update local state
                      // In a real app, this would update the auth provider's phone number

                      if (mounted) {
                        setState(() {
                          _phoneNumber = _phoneController.text;
                          _isEditingPhone = false;
                        });

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text('profile.phoneUpdatedSuccessfully'.tr)),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSizeDefault,
                      vertical: AppDimensions.paddingSizeSmall,
                    ),
                  ),
                  child: Text('profile.save'.tr),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}




