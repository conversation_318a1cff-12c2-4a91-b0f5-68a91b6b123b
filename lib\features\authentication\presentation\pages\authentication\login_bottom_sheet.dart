import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class LoginBottomSheet extends ConsumerStatefulWidget {
  const LoginBottomSheet({super.key});

  @override
  ConsumerState<LoginBottomSheet> createState() => _LoginBottomSheetState();
}

class _LoginBottomSheetState extends ConsumerState<LoginBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _inputController = TextEditingController();
  final _otpController = TextEditingController();

  // Selection state
  bool isWhatsAppSelected = true; // Default to WhatsApp
  String selectedCountryCode = '+91'; // Default country code

  // OTP state
  bool _showOTPSection = false;
  String? _sentToInput; // Store the input (email/phone) where OTP was sent

  // Country codes list
  final List<Map<String, String>> countryCodes = [
    {'code': '+1', 'country': 'US', 'flag': '🇺🇸'},
    {'code': '+44', 'country': 'UK', 'flag': '🇬🇧'},
    {'code': '+91', 'country': 'IN', 'flag': '🇮🇳'},
    {'code': '+971', 'country': 'UAE', 'flag': '🇦🇪'},
    {'code': '+966', 'country': 'SA', 'flag': '🇸🇦'},
    {'code': '+974', 'country': 'QA', 'flag': '🇶🇦'},
    {'code': '+965', 'country': 'KW', 'flag': '🇰🇼'},
    {'code': '+973', 'country': 'BH', 'flag': '🇧🇭'},
    {'code': '+968', 'country': 'OM', 'flag': '🇴🇲'},
    {'code': '+20', 'country': 'EG', 'flag': '🇪🇬'},
  ];

  @override
  void dispose() {
    _inputController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  // Send OTP method using AuthProvider
  Future<void> _sendOTP() async {
    if (!_formKey.currentState!.validate()) return;

    final authNotifier = ref.read(authProvider.notifier);
    
    String fullInput = isWhatsAppSelected
        ? '$selectedCountryCode${_inputController.text}'
        : _inputController.text;

    // For demo purposes, simulate OTP sending
    bool success = true;
    
    if (success && mounted) {
      setState(() {
        _showOTPSection = true;
        _sentToInput = fullInput;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('OTP sent to $fullInput'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(ref.read(authProvider).error ?? 'Failed to send OTP. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Verify OTP method using AuthProvider
  Future<void> _verifyOTP() async {
    if (_otpController.text.length != 4) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please enter a valid 4-digit OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final authNotifier = ref.read(authProvider.notifier);

    await authNotifier.loginWithPhone(_sentToInput!, _otpController.text);
    bool success = ref.read(authProvider).error == null;

    if (success && mounted) {
      // Close bottom sheet
      Navigator.of(context).pop();

      // Determine navigation route based on profile completion status
      final navigationRoute = await authNotifier.getNavigationRoute();

      if (mounted) {
        if (navigationRoute == '/profile-completion') {
          Navigator.pushNamed(context, '/profile-completion');
        } else {
          // User already has complete profile, stay on dashboard
          // Dashboard is already showing, no need to navigate
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login successful!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(ref.read(authProvider).error ?? 'OTP verification failed. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Handle Google login
  Future<void> _handleGoogleLogin() async {
    final authNotifier = ref.read(authProvider.notifier);

    // For demo purposes, simulate Google login with email
    const demoEmail = '<EMAIL>';
    await authNotifier.loginWithEmail(demoEmail, 'password');
    bool success = ref.read(authProvider).error == null;

    if (success && mounted) {
      // Close bottom sheet
      Navigator.of(context).pop();

      // Determine navigation route based on profile completion status
      final navigationRoute = await authNotifier.getNavigationRoute();

      if (mounted) {
        if (navigationRoute == '/profile-completion') {
          Navigator.pushNamed(context, '/profile-completion');
        } else {
          // User already has complete profile, stay on dashboard
          // Dashboard is already showing, no need to navigate
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google login successful!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Google login failed. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    return Builder(
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header section with logo and title
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Logo and app name
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(26),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Image.asset(
                            AppImages.logo,
                            height: 32,
                            width: 32,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'kind_ali Travel',
                          style: AppTextStyles.headline2.copyWith(
                            color: AppColors.primary,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Welcome text
                    Text(
                      'auth.welcomeBack'.tr,
                      style: AppTextStyles.headline1.copyWith(
                        color: AppColors.text,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 40,
                      height: 3,
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'auth.signInToContinue'.tr,
                      style: TextStyle(
                        color: AppColors.textLight,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              // Form content
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Show either input section or OTP section
                          if (!_showOTPSection) ...[
                            // Selection checkboxes
                            Row(
                              children: [
                                // WhatsApp selection
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isWhatsAppSelected = true;
                                      _inputController.clear();
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: isWhatsAppSelected
                                                ? AppColors.primary
                                                : AppColors.textLight,
                                            width: 2,
                                          ),
                                          color: isWhatsAppSelected
                                              ? AppColors.primary
                                              : Colors.transparent,
                                        ),
                                        child: isWhatsAppSelected
                                            ? const Icon(
                                                Icons.check,
                                                size: 14,
                                                color: Colors.white,
                                              )
                                            : null,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'auth.whatsapp'.tr,
                                        style: TextStyle(
                                          color: isWhatsAppSelected
                                              ? AppColors.primary
                                              : AppColors.textLight,
                                          fontWeight: isWhatsAppSelected
                                              ? FontWeight.w600
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(width: 30),

                                // Email selection
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isWhatsAppSelected = false;
                                      _inputController.clear();
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: !isWhatsAppSelected
                                                ? AppColors.primary
                                                : AppColors.textLight,
                                            width: 2,
                                          ),
                                          color: !isWhatsAppSelected
                                              ? AppColors.primary
                                              : Colors.transparent,
                                        ),
                                        child: !isWhatsAppSelected
                                            ? const Icon(
                                                Icons.check,
                                                size: 14,
                                                color: Colors.white,
                                              )
                                            : null,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'auth.email'.tr,
                                        style: TextStyle(
                                          color: !isWhatsAppSelected
                                              ? AppColors.primary
                                              : AppColors.textLight,
                                          fontWeight: !isWhatsAppSelected
                                              ? FontWeight.w600
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // Input field with country code for WhatsApp
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.divider),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: isWhatsAppSelected
                                  ? Row(
                                      children: [
                                        // Country code selector for WhatsApp
                                        InkWell(
                                          onTap: _showCountryCodePicker,
                                          borderRadius: const BorderRadius.only(
                                            topLeft: Radius.circular(12),
                                            bottomLeft: Radius.circular(12),
                                          ),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 16,
                                            ),
                                            decoration: BoxDecoration(
                                              border: Border(
                                                right: BorderSide(
                                                  color: AppColors.divider,
                                                ),
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  countryCodes.firstWhere(
                                                    (country) => country['code'] == selectedCountryCode,
                                                    orElse: () => countryCodes.first,
                                                  )['flag']!,
                                                  style: const TextStyle(fontSize: 16),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  selectedCountryCode,
                                                  style: TextStyle(
                                                    color: AppColors.text,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                Icon(
                                                  Icons.keyboard_arrow_down,
                                                  color: AppColors.textLight,
                                                  size: 20,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        // WhatsApp number input
                                        Expanded(
                                          child: TextFormField(
                                            controller: _inputController,
                                            keyboardType: TextInputType.phone,
                                            decoration: InputDecoration(
                                              labelText: 'auth.whatsappNumber'.tr,
                                              labelStyle: TextStyle(color: AppColors.textLight),
                                              hintText: 'auth.enterWhatsappNumber'.tr,
                                              prefixIcon: Icon(
                                                Icons.phone_outlined,
                                                color: AppColors.primary,
                                              ),
                                              border: InputBorder.none,
                                              contentPadding: const EdgeInsets.symmetric(
                                                vertical: 16,
                                                horizontal: 16,
                                              ),
                                            ),
                                            validator: (value) {
                                              if (value == null || value.isEmpty) {
                                                return 'auth.pleaseEnterWhatsappNumber'.tr;
                                              }
                                              if (value.length < 7) {
                                                return 'auth.pleaseEnterValidPhoneNumber'.tr;
                                              }
                                              return null;
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  :
                                  // Email input field
                                  TextFormField(
                                    controller: _inputController,
                                    keyboardType: TextInputType.emailAddress,
                                    decoration: InputDecoration(
                                      labelText: 'auth.email'.tr,
                                      labelStyle: TextStyle(color: AppColors.textLight),
                                      hintText: 'auth.enterEmailAddress'.tr,
                                      prefixIcon: Icon(
                                        Icons.email_outlined,
                                        color: AppColors.primary,
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: const EdgeInsets.symmetric(
                                        vertical: 16,
                                        horizontal: 20,
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'auth.pleaseEnterEmailAddress'.tr;
                                      }
                                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                                        return 'auth.pleaseEnterValidEmailAddress'.tr;
                                      }
                                      return null;
                                    },
                                  ),
                            ),

                            const SizedBox(height: 24),

                            // Continue button
                            CustombuttonWidget(
                              text: 'auth.continue'.tr,
                              backgroundColor: AppColors.primary,
                              textColor: Colors.white,
                              borderRadius: 12,
                              height: 56,
                              isFullWidth: true,
                              isLoading: authState.isLoading,
                              onPressed: authState.isLoading ? () {} : _sendOTP,
                              textStyle: AppTextStyles.button.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ] else ...[
                            // OTP Section
                            _buildOTPSection(),
                          ],

                          // Only show divider and Google login when not in OTP mode
                          if (!_showOTPSection) ...[
                            const SizedBox(height: 20),

                            // Divider
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(width: 100, child: Divider()),
                                Text(
                                  ' ${'auth.or'.tr} ',
                                  style: TextStyle(
                                    color: AppColors.textLight,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(width: 100, child: Divider())
                              ],
                            ),

                            const SizedBox(height: 20),

                            // Google login button
                            _socialLoginButton(
                              onPressed: authState.isLoading ? () {} : _handleGoogleLogin,
                              icon: Icons.g_mobiledata,
                              label: 'auth.continueWithGoogle'.tr,
                              isLoading: authState.isLoading,
                            ),

                            const SizedBox(height: 24),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build OTP section widget
  Widget _buildOTPSection() {
    final authState = ref.watch(authProvider);
    return Builder(
      builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Back button and title
            Row(
              children: [
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showOTPSection = false;
                      _otpController.clear();
                    });
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.primary,
                  ),
                ),
                Expanded(
                  child: Text(
                    'Verify OTP',
                    style: AppTextStyles.headline2.copyWith(
                      color: AppColors.text,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // Balance the back button
              ],
            ),

            const SizedBox(height: 16),

            // OTP sent message
            Text(
              'We have sent a verification code to',
              style: TextStyle(
                color: AppColors.textLight,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              _sentToInput ?? '',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // OTP input field
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.divider),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextFormField(
                controller: _otpController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                maxLength: 4,
                decoration: InputDecoration(
                  labelText: 'Enter 4-digit OTP',
                  labelStyle: TextStyle(color: AppColors.textLight),
                  hintText: '1234',
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: AppColors.primary,
                  ),
                  border: InputBorder.none,
                  counterText: '',
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 20,
                  ),
                ),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 8,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Verify button
            CustombuttonWidget(
              text: 'Verify OTP',
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
              borderRadius: 12,
              height: 56,
              isFullWidth: true,
              isLoading: authState.isLoading,
              onPressed: authState.isLoading ? () {} : _verifyOTP,
              textStyle: AppTextStyles.button.copyWith(
                fontSize: 18,
              ),
            ),

            const SizedBox(height: 16),

            // Resend OTP
            TextButton(
              onPressed: authState.isLoading ? null : () {
                _sendOTP(); // Resend OTP
              },
              child: Text(
                'Resend OTP',
                style: TextStyle(
                  color: authState.isLoading ? AppColors.textLight : AppColors.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCountryCodePicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'auth.selectCountryCode'.tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                ),
              ),

              // Country list
              Expanded(
                child: ListView.builder(
                  itemCount: countryCodes.length,
                  itemBuilder: (context, index) {
                    final country = countryCodes[index];
                    final isSelected = country['code'] == selectedCountryCode;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          selectedCountryCode = country['code']!;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.primary.withAlpha(20)
                              : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Text(
                              country['flag']!,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              country['country']!,
                              style: TextStyle(
                                color: AppColors.text,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              country['code']!,
                              style: TextStyle(
                                color: isSelected
                                    ? AppColors.primary
                                    : AppColors.textLight,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.check,
                                color: AppColors.primary,
                                size: 20,
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _socialLoginButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.divider),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading)
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              )
            else
              Icon(
                icon,
                size: 24,
                color: AppColors.primary,
              ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: AppColors.text,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Static method to show the login bottom sheet
  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => const LoginBottomSheet(),
    );
  }
}