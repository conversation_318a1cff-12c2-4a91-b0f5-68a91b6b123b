﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../repositories/search_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for adding to recent searches
class AddToRecentSearches implements UseCase<void, AddToRecentSearchesParams> {
  final SearchRepository repository;

  AddToRecentSearches(this.repository);

  @override
  Future<Either<Failure, void>> call(AddToRecentSearchesParams params) async {
    return await repository.addToRecentSearches(params.searchQuery);
  }
}

/// Parameters for AddToRecentSearches use case
class AddToRecentSearchesParams extends Equatable {
  final String searchQuery;

  const AddToRecentSearchesParams({required this.searchQuery});

  @override
  List<Object> get props => [searchQuery];
}

