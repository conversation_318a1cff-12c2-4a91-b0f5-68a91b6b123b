﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';
import '../../repositories/search_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for getting all cities
class GetCities implements UseCaseNoParams<List<CityEntity>> {
  final SearchRepository repository;

  GetCities(this.repository);

  @override
  Future<Either<Failure, List<CityEntity>>> call() async {
    return await repository.getCities();
  }
}

