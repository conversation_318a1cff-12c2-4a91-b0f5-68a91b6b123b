﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../repositories/auth_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';

/// Use case for logout
class Logout implements UseCaseNoParams<void> {
  final AuthRepository repository;

  Logout(this.repository);

  @override
  Future<Either<Failure, void>> call() async {
    return await repository.logout();
  }
}

