# PowerShell script to fix critical remaining import issues

Write-Host "🔧 CRITICAL IMPORT FIXES - Starting..." -ForegroundColor Yellow

# Get all Dart files
$dartFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart"
$updatedFiles = 0

# Fix 1: String extensions for .tr method
Write-Host "🔧 Fixing string extensions..." -ForegroundColor Cyan
foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match "package:kind_ali/core/extensions/string_extensions.dart") {
        $content = $content -replace "package:kind_ali/core/extensions/string_extensions.dart", "package:kind_ali/core/utils/string_extention_helper.dart"
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Fixed string extensions in: $($file.Name)" -ForegroundColor Green
    }
}

# Fix 2: Relative failure imports
Write-Host "🔧 Fixing failure imports..." -ForegroundColor Cyan
foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $updated = $false
    
    if ($content -match "\.\./\.\./\.\./core/error/failures\.dart") {
        $content = $content -replace "\.\./\.\./\.\./core/error/failures\.dart", "package:kind_ali/core/error/failures.dart"
        $updated = $true
    }
    
    if ($content -match "\.\./\.\./\.\./\.\./core/error/failures\.dart") {
        $content = $content -replace "\.\./\.\./\.\./\.\./core/error/failures\.dart", "package:kind_ali/core/error/failures.dart"
        $updated = $true
    }
    
    if ($updated) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Fixed failure imports in: $($file.Name)" -ForegroundColor Green
    }
}

# Fix 3: Network info imports
Write-Host "🔧 Fixing network info imports..." -ForegroundColor Cyan
foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match "\.\./\.\./\.\./core/network/network_info\.dart") {
        $content = $content -replace "\.\./\.\./\.\./core/network/network_info\.dart", "package:kind_ali/core/network/network_info.dart"
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Fixed network info in: $($file.Name)" -ForegroundColor Green
    }
}

# Fix 4: Base usecase imports
Write-Host "🔧 Fixing usecase imports..." -ForegroundColor Cyan
foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match "\.\./base/usecase\.dart") {
        $content = $content -replace "\.\./base/usecase\.dart", "package:kind_ali/core/utils/typedef.dart"
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Fixed usecase import in: $($file.Name)" -ForegroundColor Green
    }
}

# Fix 5: Widget imports
Write-Host "🔧 Fixing widget imports..." -ForegroundColor Cyan
foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $updated = $false
    
    if ($content -match "package:kind_ali/widgets/ArcAvatarLoader_widget\.dart") {
        $content = $content -replace "package:kind_ali/widgets/ArcAvatarLoader_widget\.dart", "package:kind_ali/shared/presentation/widgets/ArcAvatarLoader_widget.dart"
        $updated = $true
    }
    
    if ($content -match "package:kind_ali/widgets/custombutton_widget\.dart") {
        $content = $content -replace "package:kind_ali/widgets/custombutton_widget\.dart", "package:kind_ali/shared/presentation/widgets/custombutton_widget.dart"
        $updated = $true
    }
    
    if ($updated) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Fixed widget imports in: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "🎉 CRITICAL FIXES COMPLETE!" -ForegroundColor Green
Write-Host "✅ Files updated: $updatedFiles" -ForegroundColor Green
Write-Host ""
